"""
问题4：给出短时间内提升运动者11跳远成绩的姿势训练建议，以及经过短期训练后可能达到的理想跳远成绩
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from data_preprocessing import DataPreprocessor
from problem1 import Problem1Solver
from problem2 import Problem2Solver
from problem3 import Problem3Solver

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class Problem4Solver:
    """问题4求解器：训练建议和理想成绩预测"""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.problem1_solver = Problem1Solver()
        self.problem2_solver = Problem2Solver()
        self.problem3_solver = Problem3Solver()
    
    def analyze_improvement_potential(self, athlete11_data, prediction_result, improvement_analysis):
        """分析运动者11的改善潜力"""
        print("分析运动者11的改善潜力...")
        
        current_features = athlete11_data['features']
        current_score = prediction_result['predicted_score']
        
        # 分析各项技术指标与最佳表现者的差距
        improvement_potential = {}
        
        # 获取改善最大的运动员的特征作为参考
        best_improvement_athlete = max(improvement_analysis.items(), 
                                     key=lambda x: x[1]['score_improvement'])
        best_athlete_name = best_improvement_athlete[0]
        best_improvement = best_improvement_athlete[1]['score_improvement']
        
        print(f"参考最佳改善案例: {best_athlete_name} (改善{best_improvement:.2f}米)")
        
        # 分析关键技术指标的改善空间
        key_factors = [
            'airborne_duration', 'horizontal_displacement', 'max_height',
            'takeoff_velocity', 'knee_extension', 'body_stability'
        ]
        
        for factor in key_factors:
            current_value = current_features.get(factor, 0)
            
            # 基于训练数据计算该因素的改善潜力
            potential_improvement = self._calculate_factor_improvement_potential(
                factor, current_value, improvement_analysis
            )
            
            improvement_potential[factor] = potential_improvement
        
        return improvement_potential
    
    def _calculate_factor_improvement_potential(self, factor, current_value, improvement_analysis):
        """计算单个因素的改善潜力"""
        improvements = []
        
        for athlete_data in improvement_analysis.values():
            if 'feature_changes' in athlete_data and factor in athlete_data['feature_changes']:
                change = athlete_data['feature_changes'][factor]
                score_improvement = athlete_data['score_improvement']
                
                if score_improvement > 0:  # 只考虑有改善的案例
                    improvements.append({
                        'factor_change': change,
                        'score_improvement': score_improvement,
                        'improvement_ratio': score_improvement / abs(change) if change != 0 else 0
                    })
        
        if improvements:
            avg_improvement_ratio = np.mean([imp['improvement_ratio'] for imp in improvements])
            max_factor_change = np.max([abs(imp['factor_change']) for imp in improvements])
            
            # 估算该因素的改善潜力
            potential_change = max_factor_change * 0.8  # 保守估计80%的改善空间
            potential_score_improvement = potential_change * avg_improvement_ratio
            
            return {
                'current_value': current_value,
                'potential_change': potential_change,
                'potential_score_improvement': potential_score_improvement,
                'improvement_examples': len(improvements)
            }
        
        return {
            'current_value': current_value,
            'potential_change': 0,
            'potential_score_improvement': 0,
            'improvement_examples': 0
        }
    
    def generate_training_suggestions(self, athlete11_data, improvement_potential):
        """生成训练建议"""
        print("生成训练建议...")
        
        suggestions = []
        current_features = athlete11_data['features']
        physical_info = athlete11_data['physical_info']
        
        # 1. 基于滞空时间的建议
        airtime = current_features.get('airborne_duration', 0)
        if airtime < 0.4:
            suggestions.append({
                'category': '爆发力训练',
                'problem': f'滞空时间偏短({airtime:.3f}秒)',
                'suggestion': '加强下肢爆发力训练：深蹲跳、立定跳跃练习',
                'training_plan': [
                    '深蹲跳：3组×10次，每组间歇2分钟',
                    '单腿跳：左右各3组×8次',
                    '连续立定跳：3组×5次，注重起跳技术'
                ],
                'expected_improvement': improvement_potential.get('airborne_duration', {}).get('potential_score_improvement', 0)
            })
        
        # 2. 基于起跳速度的建议
        takeoff_velocity = current_features.get('takeoff_velocity', 0)
        if takeoff_velocity < 30:
            suggestions.append({
                'category': '起跳技术',
                'problem': f'起跳速度不足({takeoff_velocity:.2f})',
                'suggestion': '改善起跳技术和协调性',
                'training_plan': [
                    '摆臂练习：强化起跳时的手臂协调摆动',
                    '起跳节奏训练：练习助跑-起跳的节奏感',
                    '爆发力起跳：原地起跳练习，注重瞬间发力'
                ],
                'expected_improvement': improvement_potential.get('takeoff_velocity', {}).get('potential_score_improvement', 0)
            })
        
        # 3. 基于膝关节伸展的建议
        knee_extension = current_features.get('knee_extension', 0)
        if knee_extension < 30:
            suggestions.append({
                'category': '关节灵活性',
                'problem': f'膝关节伸展不充分({knee_extension:.1f}度)',
                'suggestion': '提高膝关节伸展能力和腿部力量',
                'training_plan': [
                    '腿部伸展练习：静态和动态拉伸',
                    '股四头肌强化：腿举、深蹲等力量训练',
                    '关节活动度训练：提高膝关节活动范围'
                ],
                'expected_improvement': improvement_potential.get('knee_extension', {}).get('potential_score_improvement', 0)
            })
        
        # 4. 基于身体稳定性的建议
        body_stability = current_features.get('body_stability', 0)
        if body_stability > 15:
            suggestions.append({
                'category': '核心稳定性',
                'problem': f'腾空期身体稳定性不足({body_stability:.2f})',
                'suggestion': '加强核心力量和身体控制能力',
                'training_plan': [
                    '核心力量训练：平板支撑、俄罗斯转体',
                    '平衡训练：单腿站立、平衡板练习',
                    '身体控制练习：空中姿态保持训练'
                ],
                'expected_improvement': improvement_potential.get('body_stability', {}).get('potential_score_improvement', 0)
            })
        
        # 5. 基于体质特征的个性化建议
        if physical_info:
            age = physical_info['年龄 (岁)']
            height = physical_info['身高 (cm)']
            weight = physical_info['体重 (kg)']
            body_fat = physical_info['体脂率 (%)']
            
            if age < 10:  # 儿童特殊建议
                suggestions.append({
                    'category': '年龄适应性训练',
                    'problem': f'年龄较小({age}岁)，需要适合儿童的训练方法',
                    'suggestion': '采用游戏化训练方式，注重基础动作模式',
                    'training_plan': [
                        '游戏化跳跃练习：跳房子、跳绳等',
                        '基础协调性训练：简单的动作组合',
                        '柔韧性训练：适合儿童的拉伸练习'
                    ],
                    'expected_improvement': 0.1
                })
            
            if body_fat > 18:
                suggestions.append({
                    'category': '体重管理',
                    'problem': f'体脂率偏高({body_fat:.1f}%)，影响跳跃表现',
                    'suggestion': '结合有氧运动降低体脂率',
                    'training_plan': [
                        '有氧运动：每周3次，每次30分钟',
                        '饮食调整：控制热量摄入',
                        '力量训练：提高肌肉质量'
                    ],
                    'expected_improvement': 0.05
                })
        
        return suggestions
    
    def predict_ideal_score(self, athlete11_data, prediction_result, training_suggestions):
        """预测理想成绩"""
        print("预测理想成绩...")
        
        current_score = prediction_result['predicted_score']
        
        # 计算各项改善的累积效果
        total_improvement = 0
        improvement_breakdown = {}
        
        for suggestion in training_suggestions:
            expected_improvement = suggestion.get('expected_improvement', 0)
            if expected_improvement > 0:
                # 应用递减效应：多项改善的叠加效果会递减
                actual_improvement = expected_improvement * 0.8  # 80%的实现率
                total_improvement += actual_improvement
                improvement_breakdown[suggestion['category']] = actual_improvement
        
        # 应用总体递减效应
        if total_improvement > 0.3:  # 如果总改善超过0.3米，应用递减
            total_improvement = 0.3 + (total_improvement - 0.3) * 0.5
        
        ideal_score = current_score + total_improvement
        
        # 基于年龄和体质的调整
        physical_info = athlete11_data['physical_info']
        if physical_info:
            age = physical_info['年龄 (岁)']
            if age < 10:  # 儿童有更大的改善潜力
                ideal_score += 0.1
            elif age > 30:  # 成年人改善潜力相对较小
                ideal_score -= 0.05
        
        return {
            'current_score': current_score,
            'ideal_score': ideal_score,
            'total_improvement': total_improvement,
            'improvement_breakdown': improvement_breakdown
        }
    
    def create_training_plan(self, training_suggestions):
        """制定详细训练计划"""
        print("制定详细训练计划...")
        
        # 按优先级排序建议
        priority_order = ['爆发力训练', '起跳技术', '关节灵活性', '核心稳定性', '年龄适应性训练', '体重管理']
        
        sorted_suggestions = []
        for category in priority_order:
            for suggestion in training_suggestions:
                if suggestion['category'] == category:
                    sorted_suggestions.append(suggestion)
        
        # 制定4周训练计划
        training_plan = {
            'duration': '4周',
            'frequency': '每周3-4次',
            'weekly_plan': {}
        }
        
        for week in range(1, 5):
            week_plan = {
                'focus': '',
                'sessions': []
            }
            
            if week == 1:
                week_plan['focus'] = '基础能力建立'
                intensity = 0.6
            elif week == 2:
                week_plan['focus'] = '技术动作强化'
                intensity = 0.7
            elif week == 3:
                week_plan['focus'] = '综合能力提升'
                intensity = 0.8
            else:
                week_plan['focus'] = '技术整合与测试'
                intensity = 0.9
            
            # 为每周安排3-4次训练
            for session in range(1, 4):
                session_plan = {
                    'warm_up': '10分钟动态热身',
                    'main_training': [],
                    'cool_down': '10分钟拉伸放松'
                }
                
                # 根据建议安排主要训练内容
                for i, suggestion in enumerate(sorted_suggestions[:3]):  # 每次训练重点关注3个方面
                    if (session - 1) % len(sorted_suggestions) == i:
                        for plan_item in suggestion['training_plan']:
                            session_plan['main_training'].append(f"{plan_item} (强度{intensity:.1f})")
                
                week_plan['sessions'].append(session_plan)
            
            training_plan['weekly_plan'][f'第{week}周'] = week_plan
        
        return training_plan
    
    def visualize_improvement_plan(self, athlete11_data, prediction_result, ideal_prediction, 
                                 training_suggestions, save_path=None):
        """可视化改善计划"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 成绩改善预测
        ax1 = axes[0, 0]
        scores = [prediction_result['predicted_score'], ideal_prediction['ideal_score']]
        labels = ['当前预测成绩', '理想成绩']
        colors = ['lightblue', 'lightgreen']
        
        bars = ax1.bar(labels, scores, color=colors)
        ax1.set_title('成绩改善预测')
        ax1.set_ylabel('成绩(米)')
        
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{score:.2f}m', ha='center', va='bottom')
        
        # 添加改善幅度标注
        improvement = ideal_prediction['total_improvement']
        ax1.annotate(f'+{improvement:.2f}m', 
                    xy=(1, scores[1]), xytext=(1, scores[1] + 0.1),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    ha='center', color='red', fontweight='bold')
        
        # 2. 改善因素分解
        ax2 = axes[0, 1]
        breakdown = ideal_prediction['improvement_breakdown']
        if breakdown:
            categories = list(breakdown.keys())
            improvements = list(breakdown.values())
            
            bars = ax2.barh(categories, improvements, color='orange')
            ax2.set_title('改善因素分解')
            ax2.set_xlabel('预期改善(米)')
            
            for bar, imp in zip(bars, improvements):
                width = bar.get_width()
                ax2.text(width, bar.get_y() + bar.get_height()/2.,
                        f'{imp:.3f}', ha='left', va='center')
        
        # 3. 训练建议优先级
        ax3 = axes[1, 0]
        suggestion_categories = [s['category'] for s in training_suggestions]
        expected_improvements = [s.get('expected_improvement', 0) for s in training_suggestions]
        
        bars = ax3.bar(range(len(suggestion_categories)), expected_improvements, 
                      color=['red', 'orange', 'yellow', 'green', 'blue', 'purple'][:len(suggestion_categories)])
        ax3.set_title('训练建议优先级')
        ax3.set_xlabel('训练类别')
        ax3.set_ylabel('预期改善(米)')
        ax3.set_xticks(range(len(suggestion_categories)))
        ax3.set_xticklabels(suggestion_categories, rotation=45, ha='right')
        
        # 4. 训练时间规划
        ax4 = axes[1, 1]
        weeks = ['第1周', '第2周', '第3周', '第4周']
        focus_areas = ['基础能力', '技术强化', '综合提升', '技术整合']
        intensities = [0.6, 0.7, 0.8, 0.9]
        
        bars = ax4.bar(weeks, intensities, color=['lightcoral', 'gold', 'lightgreen', 'skyblue'])
        ax4.set_title('4周训练强度规划')
        ax4.set_ylabel('训练强度')
        ax4.set_ylim(0, 1)
        
        for bar, focus in zip(bars, focus_areas):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    focus, ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"改善计划图已保存为: {save_path}")
        
        plt.show()
        return fig
    
    def solve_problem4(self):
        """求解问题4"""
        print("=" * 60)
        print("问题4：训练建议和理想成绩预测")
        print("=" * 60)
        
        # 1. 获取问题3的结果
        problem3_results = self.problem3_solver.solve_problem3()
        if problem3_results is None:
            print("无法获取问题3的结果")
            return None
        
        athlete11_data = problem3_results['athlete11_data']
        prediction_result = problem3_results['prediction_result']
        
        # 2. 获取问题2的改善分析结果
        problem2_results = self.problem2_solver.solve_problem2()
        improvement_analysis = problem2_results['improvement_analysis']
        
        # 3. 分析改善潜力
        improvement_potential = self.analyze_improvement_potential(
            athlete11_data, prediction_result, improvement_analysis
        )
        
        # 4. 生成训练建议
        training_suggestions = self.generate_training_suggestions(
            athlete11_data, improvement_potential
        )
        
        # 5. 预测理想成绩
        ideal_prediction = self.predict_ideal_score(
            athlete11_data, prediction_result, training_suggestions
        )
        
        # 6. 制定训练计划
        training_plan = self.create_training_plan(training_suggestions)
        
        # 7. 可视化改善计划
        self.visualize_improvement_plan(
            athlete11_data, prediction_result, ideal_prediction, 
            training_suggestions, 'problem4_improvement_plan.png'
        )
        
        # 8. 输出详细建议
        self._print_training_suggestions(training_suggestions, ideal_prediction, training_plan)
        
        return {
            'training_suggestions': training_suggestions,
            'ideal_prediction': ideal_prediction,
            'training_plan': training_plan,
            'improvement_potential': improvement_potential
        }
    
    def _print_training_suggestions(self, training_suggestions, ideal_prediction, training_plan):
        """输出训练建议"""
        print("\n" + "=" * 60)
        print("运动者11训练建议和理想成绩预测")
        print("=" * 60)
        
        print(f"\n成绩预测:")
        print(f"  当前预测成绩: {ideal_prediction['current_score']:.2f}米")
        print(f"  理想成绩预测: {ideal_prediction['ideal_score']:.2f}米")
        print(f"  预期改善幅度: {ideal_prediction['total_improvement']:.2f}米")
        
        print(f"\n训练建议 (按优先级排序):")
        for i, suggestion in enumerate(training_suggestions, 1):
            print(f"\n{i}. {suggestion['category']}")
            print(f"   问题: {suggestion['problem']}")
            print(f"   建议: {suggestion['suggestion']}")
            print(f"   预期改善: {suggestion.get('expected_improvement', 0):.3f}米")
            print(f"   训练计划:")
            for plan_item in suggestion['training_plan']:
                print(f"     - {plan_item}")
        
        print(f"\n4周训练计划概要:")
        print(f"  训练周期: {training_plan['duration']}")
        print(f"  训练频率: {training_plan['frequency']}")
        
        for week, plan in training_plan['weekly_plan'].items():
            print(f"\n  {week}: {plan['focus']}")
            print(f"    训练次数: {len(plan['sessions'])}次")
        
        print(f"\n改善效果分解:")
        for category, improvement in ideal_prediction['improvement_breakdown'].items():
            print(f"  {category}: +{improvement:.3f}米")


def main():
    """主函数"""
    solver = Problem4Solver()
    results = solver.solve_problem4()


if __name__ == "__main__":
    main()
