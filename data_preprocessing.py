"""
数据预处理模块
负责加载和预处理所有附件数据
"""

import numpy as np
import pandas as pd
import os
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d, UnivariateSpline
from scipy.ndimage import gaussian_filter1d

class DataPreprocessor:
    """数据预处理类"""
    
    def __init__(self):
        # MediaPipe 33个关键点定义
        self.keypoint_names = {
            0: '鼻子', 
            1: '左眼内侧', 2: '左眼', 3: '左眼外侧',
            4: '右眼内侧', 5: '右眼', 6: '右眼外侧',
            7: '左耳', 8: '右耳', 
            9: '嘴巴左侧', 10: '嘴巴右侧',
            11: '左肩', 12: '右肩', 
            13: '左肘', 14: '右肘',
            15: '左手腕', 16: '右手腕', 
            17: '左小指', 18: '右小指',
            19: '左食指', 20: '右食指',
            21: '左拇指', 22: '右拇指',
            23: '左髋', 24: '右髋',
            25: '左膝', 26: '右膝',
            27: '左脚踝', 28: '右脚踝',
            29: '左脚跟', 30: '右脚跟',
            31: '左脚尖', 32: '右脚尖'
        }

    # 足部相关关键点索引
        self.foot_keypoints = {
            '左脚踝': 27, '右脚踝': 28,
            '左脚跟': 29, '右脚跟': 30,
            '左脚尖': 31, '右脚尖': 32
        }

        # 身体核心关键点索引
        self.body_keypoints = {
            '左髋': 23, '右髋': 24,
            '左膝': 25, '右膝': 26,
            '左肩': 11, '右肩': 12,
            '左肘': 13, '右肘': 14,
            '左手腕': 15, '右手腕': 16
        }

        
        # 数据存储
        self.all_data = {}
        self.physical_data = None
    
    def load_position_data(self, filepath):
        """加载位置信息数据并处理缺失值"""
        df = pd.read_excel(filepath)
        coordinates = []

        for frame_idx in range(len(df)):
            frame_points = []
            for point_idx in range(33):
                x_col = f"{point_idx}_X"
                y_col = f"{point_idx}_Y"
                if x_col in df.columns and y_col in df.columns:
                    x = df.iloc[frame_idx][x_col]
                    y = df.iloc[frame_idx][y_col]

                    # 将0值视为缺失数据，转换为NaN
                    if x == 0 and y == 0:
                        x, y = np.nan, np.nan

                    frame_points.append([x, y])
            coordinates.append(frame_points)

        coordinates = np.array(coordinates)

        # 检查整体缺失率，决定是否排除该运动员
        total_points = coordinates.size
        missing_points = np.sum(np.isnan(coordinates))
        missing_rate = missing_points / total_points

        if missing_rate > 0.2:
            print(f"  数据缺失率{missing_rate:.1%}超过20%，排除该运动员")
            return None

        # 对每个关键点进行缺失数据填充
        coordinates = self._fill_missing_coordinates(coordinates)

        return coordinates

    def _fill_missing_coordinates(self, coordinates):
        """填充缺失的坐标数据"""
        print("  处理坐标数据中的缺失值...")

        num_frames, num_points, num_coords = coordinates.shape
        filled_coordinates = coordinates.copy()

        total_missing = 0
        total_filled = 0
        excluded_points = []

        # 对每个关键点的每个坐标维度进行处理
        for point_idx in range(num_points):
            for coord_idx in range(num_coords):  # 0=x, 1=y
                coord_data = coordinates[:, point_idx, coord_idx]

                # 检查缺失数据
                missing_mask = np.isnan(coord_data)
                missing_count = np.sum(missing_mask)
                total_missing += missing_count

                if missing_count > 0:
                    missing_rate = missing_count / num_frames
                    coord_name = 'X' if coord_idx == 0 else 'Y'

                    if missing_rate > 0.2:
                        # 缺失率超过20%，排除该关键点
                        excluded_points.append(f"关键点{point_idx}_{coord_name}")
                        filled_coordinates[:, point_idx, coord_idx] = 0  # 设为0表示无效
                        print(f"    关键点{point_idx}_{coord_name}: 缺失{missing_rate:.1%}, 排除该关键点")
                    elif missing_rate > 0.05:
                        # 缺失率5%-20%，使用样条插值
                        filled_data = self._spline_fill_coordinates(coord_data)
                        filled_coordinates[:, point_idx, coord_idx] = filled_data
                        total_filled += missing_count
                        print(f"    关键点{point_idx}_{coord_name}: 缺失{missing_rate:.1%}, 使用样条插值填充")
                    else:
                        # 缺失率<5%，使用线性插值
                        filled_data = self._linear_fill_coordinates(coord_data)
                        filled_coordinates[:, point_idx, coord_idx] = filled_data
                        total_filled += missing_count
                        print(f"    关键点{point_idx}_{coord_name}: 缺失{missing_rate:.1%}, 使用线性插值填充")

        print(f"  坐标数据处理完成：总缺失{total_missing}个，已填充{total_filled}个")
        if excluded_points:
            print(f"  排除的关键点：{len(excluded_points)}个")
        return filled_coordinates

    def _linear_fill_coordinates(self, coord_data):
        """线性插值填充坐标数据"""
        filled_data = coord_data.copy()
        missing_mask = np.isnan(coord_data)

        if np.all(missing_mask):
            # 全部缺失，使用0填充
            return np.zeros_like(coord_data)

        if np.any(~missing_mask):
            # 有有效数据点，进行插值
            valid_indices = np.where(~missing_mask)[0]
            valid_values = coord_data[valid_indices]

            if len(valid_indices) == 1:
                # 只有一个有效点，用该值填充所有缺失
                filled_data[missing_mask] = valid_values[0]
            else:
                # 使用线性插值
                interp_func = interp1d(valid_indices, valid_values,
                                     kind='linear', fill_value='extrapolate')
                missing_indices = np.where(missing_mask)[0]
                filled_data[missing_indices] = interp_func(missing_indices)

        return filled_data

    def _spline_fill_coordinates(self, coord_data):
        """样条插值填充坐标数据"""
        filled_data = coord_data.copy()
        missing_mask = np.isnan(coord_data)

        if np.all(missing_mask):
            # 全部缺失，使用0填充
            return np.zeros_like(coord_data)

        if np.any(~missing_mask):
            valid_indices = np.where(~missing_mask)[0]
            valid_values = coord_data[valid_indices]

            if len(valid_indices) < 3:
                # 有效点少于3个，降级为线性插值
                return self._linear_fill_coordinates(coord_data)

            try:
                # 使用样条插值
                spline_func = UnivariateSpline(valid_indices, valid_values, s=0)
                missing_indices = np.where(missing_mask)[0]
                filled_data[missing_indices] = spline_func(missing_indices)
            except:
                # 样条插值失败，降级为线性插值
                return self._linear_fill_coordinates(coord_data)

        return filled_data


    
    def load_attachment1_data(self):
        """加载附件1数据：两位运动者的基础数据"""
        print("加载附件1数据...")

        # 读取成绩
        with open('附件/附件1/运动者1和运动者2的跳远成绩.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()

        athlete1_score = None
        athlete2_score = None

        for line in lines:
            line = line.strip()
            if '运动者1' in line:
                import re
                # 匹配小数格式的成绩（如1.58）
                scores = re.findall(r'(\d+\.\d+)', line)
                athlete1_score = float(scores[0]) if scores else None
            elif '运动者2' in line:
                import re
                # 匹配小数格式的成绩（如1.15）
                scores = re.findall(r'(\d+\.\d+)', line)
                athlete2_score = float(scores[0]) if scores else None
        
        # 加载位置数据
        coordinates1 = self.load_position_data('附件/附件1/运动者1的跳远位置信息.xlsx')
        coordinates2 = self.load_position_data('附件/附件1/运动者2的跳远位置信息.xlsx')
        
        self.all_data['attachment1'] = {
            'athlete1': {
                'coordinates': coordinates1,
                'score': athlete1_score,
                'name': '运动者1'
            },
            'athlete2': {
                'coordinates': coordinates2,
                'score': athlete2_score,
                'name': '运动者2'
            }
        }
        
        print(f"附件1加载完成：运动者1({athlete1_score}m), 运动者2({athlete2_score}m)")
        return self.all_data['attachment1']
    
    def load_attachment3_data(self):
        """加载附件3数据：姿势调整前后对比数据"""
        print("加载附件3数据...")
        
        # 解析成绩文件
        before_scores = self._parse_scores('附件/附件3/姿势调整前/运动者姿势调整前的跳远成绩.txt')
        after_scores = self._parse_scores('附件/附件3/姿势调整后/运动者姿势调整后的跳远成绩.txt')
        
        attachment3_data = {'before': {}, 'after': {}}
        
        # 加载调整前数据
        before_dir = '附件/附件3/姿势调整前'
        for filename in os.listdir(before_dir):
            if filename.endswith('.xlsx'):
                athlete_info = self._parse_filename(filename)
                if athlete_info:
                    athlete_name, attempt = athlete_info
                    score = before_scores.get(athlete_name, {}).get(f'第{attempt}次', None)
                    
                    try:
                        coordinates = self.load_position_data(os.path.join(before_dir, filename))
                        key = f'{athlete_name}_第{attempt}次'
                        attachment3_data['before'][key] = {
                            'coordinates': coordinates,
                            'score': score,
                            'athlete': athlete_name,
                            'attempt': attempt
                        }
                    except Exception as e:
                        print(f"加载{filename}失败: {e}")
        
        # 加载调整后数据
        after_dir = '附件/附件3/姿势调整后'
        for filename in os.listdir(after_dir):
            if filename.endswith('.xlsx'):
                athlete_info = self._parse_filename(filename.replace('调整后', ''))
                if athlete_info:
                    athlete_name, attempt = athlete_info
                    score = after_scores.get(f'{athlete_name}调整后', {}).get(f'第{attempt}次', None)
                    
                    try:
                        coordinates = self.load_position_data(os.path.join(after_dir, filename))
                        key = f'{athlete_name}_第{attempt}次'
                        attachment3_data['after'][key] = {
                            'coordinates': coordinates,
                            'score': score,
                            'athlete': athlete_name,
                            'attempt': attempt
                        }
                    except Exception as e:
                        print(f"加载{filename}失败: {e}")
        
        self.all_data['attachment3'] = attachment3_data
        print(f"附件3加载完成：调整前{len(attachment3_data['before'])}个，调整后{len(attachment3_data['after'])}个")
        return attachment3_data
    
    def load_attachment4_data(self):
        """加载附件4数据：体质报告"""
        print("加载附件4数据...")
        
        try:
            self.physical_data = pd.read_excel('附件/附件4.xlsx')
            self.all_data['attachment4'] = self.physical_data
            print(f"附件4加载完成：{len(self.physical_data)}名运动员体质数据")
            return self.physical_data
        except Exception as e:
            print(f"附件4加载失败: {e}")
            return None
    
    def load_attachment5_data(self):
        """加载附件5数据：运动者11"""
        print("加载附件5数据...")
        
        try:
            coordinates = self.load_position_data('附件/附件5/运动者11的跳远位置信息.xlsx')
            
            # 获取运动者11的体质信息
            physical_info = None
            if self.physical_data is not None:
                athlete11_row = self.physical_data[self.physical_data['姓名'] == '运动者11']
                if len(athlete11_row) > 0:
                    physical_info = athlete11_row.iloc[0].to_dict()
            
            self.all_data['attachment5'] = {
                'coordinates': coordinates,
                'physical_info': physical_info,
                'name': '运动者11'
            }
            
            print("附件5加载完成：运动者11数据")
            return self.all_data['attachment5']
        except Exception as e:
            print(f"附件5加载失败: {e}")
            return None
    
    def _parse_scores(self, score_file):
        """解析成绩文件"""
        scores = {}
        with open(score_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_athlete = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if '运动者' in line and '第' not in line:
                current_athlete = line
                scores[current_athlete] = {}
            elif '第' in line and '次' in line and current_athlete:
                parts = line.split()
                if len(parts) >= 2:
                    attempt = parts[0]
                    score_str = parts[1].replace('米', '')
                    try:
                        score = float(score_str)
                        scores[current_athlete][attempt] = score
                    except:
                        pass
        
        return scores
    
    def _parse_filename(self, filename):
        """解析文件名获取运动员和次数信息"""
        filename = filename.replace('.xlsx', '')
        parts = filename.split('第')
        if len(parts) >= 2:
            athlete_name = parts[0]
            attempt = parts[1].replace('次的跳远位置信息', '')
            return athlete_name, attempt
        return None
    
    def smooth_data(self, data, window_size=5):
        """数据平滑处理"""
        if len(data) < window_size:
            return data
        try:
            return savgol_filter(data, window_size, 3)
        except:
            return data
    
    def calculate_velocity(self, positions):
        """计算速度（帧间差分）"""
        return np.diff(positions, prepend=positions[0])
    
    def get_foot_positions(self, coordinates):
        """获取足部关键点位置"""
        foot_data = {}
        for name, idx in self.foot_keypoints.items():
            foot_data[name] = {
                'x': coordinates[:, idx, 0],
                'y': coordinates[:, idx, 1]
            }
        return foot_data
    
    def load_all_data(self):
        """加载所有附件数据"""
        print("=" * 60)
        print("开始加载所有附件数据")
        print("=" * 60)
        
        # 按顺序加载所有附件
        self.load_attachment1_data()
        self.load_attachment4_data()  # 先加载体质数据，供附件5使用
        self.load_attachment3_data()
        self.load_attachment5_data()
        
        print("=" * 60)
        print("所有数据加载完成")
        print(f"附件1: {len(self.all_data.get('attachment1', {}))}")
        print(f"附件3: 调整前{len(self.all_data.get('attachment3', {}).get('before', {}))}, "
              f"调整后{len(self.all_data.get('attachment3', {}).get('after', {}))}")
        print(f"附件4: {len(self.physical_data) if self.physical_data is not None else 0}名运动员")
        print(f"附件5: {'已加载' if 'attachment5' in self.all_data else '未加载'}")
        print("=" * 60)
        
        return self.all_data
    




    def get_physical_info(self, athlete_name):
        """获取运动员体质信息"""
        if self.physical_data is None:
            return None

        athlete_row = self.physical_data[self.physical_data['姓名'] == athlete_name]
        if len(athlete_row) > 0:
            return athlete_row.iloc[0].to_dict()
        return None


if __name__ == "__main__":
    # 测试数据预处理模块
    preprocessor = DataPreprocessor()
    all_data = preprocessor.load_all_data()
    
    print("\n数据预处理模块测试完成")
    print("可用数据:")
    for key, value in all_data.items():
        print(f"- {key}: {type(value)}")
