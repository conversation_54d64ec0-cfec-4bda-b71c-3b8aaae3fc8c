"""
问题3：预测运动者11的实际跳远成绩
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, r2_score
from data_preprocessing import DataPreprocessor
from problem1 import Problem1Solver
from problem2 import Problem2Solver

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class Problem3Solver:
    """问题3求解器：成绩预测"""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.problem1_solver = Problem1Solver()
        self.problem2_solver = Problem2Solver()
        self.scaler = StandardScaler()
        self.model = None
        self.feature_names = []
    
    def get_trained_model_from_problem2(self):
        """从问题2获取训练好的模型"""
        print("从问题2获取训练好的模型...")

        # 运行问题2获取训练好的模型（包含附件1数据）
        problem2_results = self.problem2_solver.solve_problem2()
        model_analysis = problem2_results['model_analysis']

        if model_analysis is None:
            print("问题2模型训练失败")
            return None

        print(f"获取到训练好的模型: {model_analysis['best_model_name']}")
        print(f"模型性能: R²={model_analysis['best_score']:.4f}")
        print(f"特征数量: {len(model_analysis['feature_names'])}")

        return model_analysis
    
    def setup_model(self, model_analysis):
        """设置从问题2获取的模型"""
        print("设置预测模型...")

        self.model = model_analysis['best_model']
        self.scaler = model_analysis['scaler']
        self.feature_names = model_analysis['feature_names']

        print(f"模型设置完成: {model_analysis['best_model_name']}")
        print(f"模型性能: R²={model_analysis['best_score']:.4f}")

        return model_analysis
    
    def analyze_athlete11(self):
        """分析运动者11"""
        print("分析运动者11...")
        
        # 加载运动者11数据
        attachment5_data = self.preprocessor.load_attachment5_data()
        
        if attachment5_data is None:
            print("无法加载运动者11数据")
            return None
        
        coordinates = attachment5_data['coordinates']
        physical_info = attachment5_data['physical_info']
        
        # 识别关键帧
        takeoff = self.problem1_solver.detect_takeoff_frame(coordinates)
        landing = self.problem1_solver.detect_landing_frame(coordinates, takeoff)
        
        if takeoff is None or landing is None:
            print("无法识别运动者11的关键帧")
            return None
        
        print(f"运动者11关键帧: 起跳{takeoff}, 落地{landing}")
        
        # 提取特征
        features = self.problem2_solver.extract_comprehensive_features(coordinates, takeoff, landing)
        
        if features is None:
            print("无法提取运动者11的特征")
            return None
        
        # 分析滞空阶段运动
        motion_analysis = self.problem1_solver.analyze_airborne_motion(coordinates, takeoff, landing)
        
        return {
            'coordinates': coordinates,
            'physical_info': physical_info,
            'takeoff_frame': takeoff,
            'landing_frame': landing,
            'features': features,
            'motion_analysis': motion_analysis
        }
    
    def predict_athlete11_score(self, athlete11_data):
        """预测运动者11的成绩"""
        print("预测运动者11成绩...")
        
        if self.model is None:
            print("模型尚未训练")
            return None
        
        features = athlete11_data['features']
        
        # 准备预测数据
        feature_df = pd.DataFrame([features])
        
        # 确保所有训练特征都存在
        for col in self.feature_names:
            if col not in feature_df.columns:
                feature_df[col] = 0
        
        feature_df = feature_df[self.feature_names]
        feature_df = feature_df.fillna(feature_df.mean())
        
        # 标准化
        X_pred = self.scaler.transform(feature_df)
        
        # 预测
        predicted_score = self.model.predict(X_pred)[0]
        
        print(f"预测成绩: {predicted_score:.2f}米")
        
        # 分析预测置信度
        confidence_analysis = self._analyze_prediction_confidence(athlete11_data, predicted_score)
        
        return {
            'predicted_score': predicted_score,
            'confidence_analysis': confidence_analysis,
            'features_used': features
        }
    
    def _analyze_prediction_confidence(self, athlete11_data, predicted_score):
        """分析预测置信度"""
        confidence_analysis = {}
        
        # 与训练数据的相似性分析
        physical_info = athlete11_data['physical_info']
        if physical_info:
            confidence_analysis['physical_factors'] = {
                'age': physical_info['年龄 (岁)'],
                'height': physical_info['身高 (cm)'],
                'weight': physical_info['体重 (kg)'],
                'body_fat': physical_info['体脂率 (%)'],
                'gender': physical_info['性别']
            }
        
        # 技术特征分析
        features = athlete11_data['features']
        motion = athlete11_data['motion_analysis']
        
        confidence_analysis['technical_factors'] = {
            'airborne_duration': motion['airborne_duration'],
            'horizontal_displacement': motion['horizontal_displacement'],
            'max_height': motion['max_height'],
            'takeoff_velocity': features.get('takeoff_velocity', 0),
            'knee_extension': features.get('knee_extension', 0),
            'body_stability': features.get('body_stability', 0)
        }
        
        # 预测合理性检查
        confidence_analysis['reasonableness_check'] = {
            'score_range': (0.5, 3.0),  # 合理的跳远成绩范围
            'within_range': 0.5 <= predicted_score <= 3.0,
            'compared_to_training_min': predicted_score >= 0.8,  # 训练数据最小值附近
            'compared_to_training_max': predicted_score <= 2.5   # 训练数据最大值附近
        }
        
        return confidence_analysis
    
    def visualize_prediction_analysis(self, athlete11_data, prediction_result, training_info, save_path=None):
        """可视化预测分析"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 运动者11的运动轨迹
        ax1 = axes[0, 0]
        motion = athlete11_data['motion_analysis']
        com_trajectory = motion['com_trajectory']
        
        ax1.plot(com_trajectory[:, 0], com_trajectory[:, 1], 'b-', linewidth=2, label='重心轨迹')
        ax1.scatter(com_trajectory[0, 0], com_trajectory[0, 1], color='red', s=100, label='起跳点')
        ax1.scatter(com_trajectory[-1, 0], com_trajectory[-1, 1], color='green', s=100, label='落地点')
        ax1.set_title(f'运动者11运动轨迹\n预测成绩: {prediction_result["predicted_score"]:.2f}米')
        ax1.set_xlabel('X坐标')
        ax1.set_ylabel('Y坐标')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()
        
        # 2. 关键特征对比
        ax2 = axes[0, 1]
        features = athlete11_data['features']
        key_features = ['airborne_duration', 'horizontal_displacement', 'max_height', 'takeoff_velocity']
        feature_values = [features.get(f, 0) for f in key_features]
        feature_labels = ['滞空时间', '水平位移', '腾空高度', '起跳速度']
        
        bars = ax2.bar(feature_labels, feature_values, color=['skyblue', 'lightgreen', 'orange', 'pink'])
        ax2.set_title('运动者11关键特征')
        ax2.set_ylabel('特征值')
        ax2.tick_params(axis='x', rotation=45)
        
        for bar, value in zip(bars, feature_values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.2f}', ha='center', va='bottom')
        
        # 3. 体质因素对比
        ax3 = axes[1, 0]
        physical_info = athlete11_data['physical_info']
        if physical_info:
            physical_features = ['年龄 (岁)', '身高 (cm)', '体重 (kg)', '体脂率 (%)']
            physical_values = [physical_info[f] for f in physical_features]
            physical_labels = ['年龄', '身高', '体重', '体脂率']
            
            # 标准化显示
            normalized_values = []
            for i, (value, label) in enumerate(zip(physical_values, physical_labels)):
                if label == '年龄':
                    normalized_values.append(value / 50 * 100)  # 标准化到百分比
                elif label == '身高':
                    normalized_values.append(value / 200 * 100)
                elif label == '体重':
                    normalized_values.append(value / 100 * 100)
                else:  # 体脂率
                    normalized_values.append(value)
            
            bars = ax3.bar(physical_labels, normalized_values, color=['lightcoral', 'lightblue', 'lightgreen', 'gold'])
            ax3.set_title('运动者11体质特征')
            ax3.set_ylabel('标准化值')
            
            for bar, orig_value in zip(bars, physical_values):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height,
                        f'{orig_value}', ha='center', va='bottom')
        
        # 4. 预测置信度分析
        ax4 = axes[1, 1]
        confidence = prediction_result['confidence_analysis']
        
        # 显示预测合理性检查结果
        checks = confidence['reasonableness_check']
        check_labels = ['范围内', '≥最小值', '≤最大值']
        check_values = [checks['within_range'], checks['compared_to_training_min'], checks['compared_to_training_max']]
        check_colors = ['green' if v else 'red' for v in check_values]
        
        bars = ax4.bar(check_labels, [1 if v else 0 for v in check_values], color=check_colors)
        ax4.set_title('预测合理性检查')
        ax4.set_ylabel('通过/失败')
        ax4.set_ylim(0, 1.2)
        
        for bar, passed in zip(bars, check_values):
            height = bar.get_height()
            text = '通过' if passed else '失败'
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    text, ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测分析图已保存为: {save_path}")
        
        plt.show()
        return fig
    
    def solve_problem3(self):
        """求解问题3"""
        print("=" * 60)
        print("问题3：预测运动者11的实际跳远成绩")
        print("=" * 60)

        # 1. 从问题2获取训练好的模型
        model_analysis = self.get_trained_model_from_problem2()

        if model_analysis is None:
            print("无法获取训练好的模型")
            return None

        # 2. 设置预测模型
        training_info = self.setup_model(model_analysis)

        # 3. 分析运动者11
        athlete11_data = self.analyze_athlete11()

        if athlete11_data is None:
            print("无法分析运动者11")
            return None

        # 4. 预测成绩
        prediction_result = self.predict_athlete11_score(athlete11_data)

        if prediction_result is None:
            print("无法预测运动者11成绩")
            return None

        # 5. 可视化分析结果
        self.visualize_prediction_analysis(athlete11_data, prediction_result, training_info,
                                         'problem3_prediction_analysis.png')

        # 6. 输出详细分析结果
        self._print_prediction_results(athlete11_data, prediction_result, training_info)

        return {
            'athlete11_data': athlete11_data,
            'prediction_result': prediction_result,
            'training_info': training_info,
            'model_analysis': model_analysis
        }
    
    def _print_prediction_results(self, athlete11_data, prediction_result, training_info):
        """输出预测结果"""
        print("\n" + "=" * 60)
        print("运动者11成绩预测结果")
        print("=" * 60)
        
        print(f"\n预测成绩: {prediction_result['predicted_score']:.2f}米")
        
        print(f"\n模型信息:")
        print(f"  模型类型: {training_info.get('best_model_name', 'Unknown')}")
        print(f"  模型精度: R²={training_info.get('best_score', 0):.4f}")
        print(f"  训练样本数: {len(training_info.get('feature_names', []))}个特征")
        
        print(f"\n运动者11基本信息:")
        physical_info = athlete11_data['physical_info']
        if physical_info:
            print(f"  年龄: {physical_info['年龄 (岁)']}岁")
            print(f"  身高: {physical_info['身高 (cm)']}cm")
            print(f"  体重: {physical_info['体重 (kg)']}kg")
            print(f"  体脂率: {physical_info['体脂率 (%)']}%")
            print(f"  性别: {physical_info['性别']}")
        
        print(f"\n技术特征分析:")
        motion = athlete11_data['motion_analysis']
        print(f"  滞空时间: {motion['airborne_duration']:.3f}秒")
        print(f"  水平位移: {motion['horizontal_displacement']:.2f}像素")
        print(f"  腾空高度: {motion['max_height']:.2f}像素")
        
        features = athlete11_data['features']
        print(f"  起跳速度: {features.get('takeoff_velocity', 0):.2f}")
        print(f"  膝关节伸展: {features.get('knee_extension', 0):.2f}度")
        print(f"  身体稳定性: {features.get('body_stability', 0):.2f}")
        
        print(f"\n预测置信度分析:")
        confidence = prediction_result['confidence_analysis']
        checks = confidence['reasonableness_check']
        print(f"  成绩在合理范围内: {'是' if checks['within_range'] else '否'}")
        print(f"  不低于训练最小值: {'是' if checks['compared_to_training_min'] else '否'}")
        print(f"  不高于训练最大值: {'是' if checks['compared_to_training_max'] else '否'}")


def main():
    """主函数"""
    solver = Problem3Solver()
    results = solver.solve_problem3()


if __name__ == "__main__":
    main()
