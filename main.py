"""
立定跳远建模系统主程序
按问题顺序执行所有分析
"""

import sys
import time
from data_preprocessing import DataPreprocessor
from problem1 import Problem1Solver
from problem2 import Problem2Solver
from problem3 import Problem3Solver
from problem4 import Problem4Solver

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 80)
    print(f"{title:^80}")
    print("=" * 80)

def print_separator():
    """打印分隔符"""
    print("\n" + "-" * 80)

def main():
    """主函数：按顺序执行所有问题"""
    print_header("立定跳远建模系统")
    print("基于MediaPipe关键点的立定跳远技术分析与成绩预测系统")
    print("包含问题1-4的完整解决方案")
    
    start_time = time.time()
    
    try:
        # 数据预处理
        print_header("数据预处理模块")
        preprocessor = DataPreprocessor()
        all_data = preprocessor.load_all_data()
        
        print_separator()
        print("数据预处理完成")
        print(f"- 附件1: {len(all_data.get('attachment1', {}))}")
        print(f"- 附件3: 调整前{len(all_data.get('attachment3', {}).get('before', {}))}, "
              f"调整后{len(all_data.get('attachment3', {}).get('after', {}))}")
        print(f"- 附件4: {len(preprocessor.physical_data) if preprocessor.physical_data is not None else 0}名运动员")
        print(f"- 附件5: {'已加载' if 'attachment5' in all_data else '未加载'}")
        
        # 问题1：关键时刻识别和滞空阶段分析
        print_header("问题1：关键时刻识别和滞空阶段分析")
        problem1_solver = Problem1Solver()
        problem1_results = problem1_solver.solve_problem1()
        
        if problem1_results:
            print_separator()
            print("问题1完成")
            print(f"- 成功分析{len(problem1_results)}名运动员")
            print("- 生成运动轨迹分析图")
            print("- 识别起跳和落地关键时刻")
        
        # 问题2：影响因素分析
        print_header("问题2：影响因素分析")
        problem2_solver = Problem2Solver()
        problem2_results = problem2_solver.solve_problem2()
        
        if problem2_results:
            print_separator()
            print("问题2完成")
            improvement_analysis = problem2_results['improvement_analysis']
            print(f"- 分析{len(improvement_analysis)}名运动员的改善效果")
            print("- 识别关键技术因素")
            print("- 分析体质因素影响")
        
        # 问题3：成绩预测
        print_header("问题3：运动者11成绩预测")
        problem3_solver = Problem3Solver()
        problem3_results = problem3_solver.solve_problem3()
        
        if problem3_results:
            prediction_result = problem3_results['prediction_result']
            print_separator()
            print("问题3完成")
            print(f"- 运动者11预测成绩: {prediction_result['predicted_score']:.2f}米")
            print("- 建立预测模型")
            print("- 生成预测分析图")
        
        # 问题4：训练建议和理想成绩
        print_header("问题4：训练建议和理想成绩预测")
        problem4_solver = Problem4Solver()
        problem4_results = problem4_solver.solve_problem4()
        
        if problem4_results:
            ideal_prediction = problem4_results['ideal_prediction']
            training_suggestions = problem4_results['training_suggestions']
            print_separator()
            print("问题4完成")
            print(f"- 理想成绩预测: {ideal_prediction['ideal_score']:.2f}米")
            print(f"- 预期改善: {ideal_prediction['total_improvement']:.2f}米")
            print(f"- 生成{len(training_suggestions)}项训练建议")
            print("- 制定4周训练计划")
        
        # 总结
        end_time = time.time()
        execution_time = end_time - start_time
        
        print_header("系统执行总结")
        print("所有问题分析完成！")
        print(f"\n执行时间: {execution_time:.2f}秒")
        
        print(f"\n生成的文件:")
        generated_files = [
            "problem1_运动者1_analysis.png - 运动者1运动分析图",
            "problem1_运动者2_analysis.png - 运动者2运动分析图", 
            "problem2_factor_analysis.png - 影响因素分析图",
            "problem3_prediction_analysis.png - 成绩预测分析图",
            "problem4_improvement_plan.png - 训练改善计划图"
        ]
        
        for i, file_desc in enumerate(generated_files, 1):
            print(f"{i}. {file_desc}")
        
        print(f"\n主要结果:")
        if problem1_results:
            print("✓ 问题1: 成功识别关键时刻，分析滞空阶段运动过程")
        if problem2_results:
            print("✓ 问题2: 识别影响跳远成绩的主要因素")
        if problem3_results:
            predicted_score = problem3_results['prediction_result']['predicted_score']
            print(f"✓ 问题3: 预测运动者11成绩 {predicted_score:.2f}米")
        if problem4_results:
            ideal_score = problem4_results['ideal_prediction']['ideal_score']
            improvement = problem4_results['ideal_prediction']['total_improvement']
            print(f"✓ 问题4: 理想成绩 {ideal_score:.2f}米 (改善{improvement:.2f}米)")
        
        print(f"\n技术特点:")
        print("• 基于MediaPipe 33个标准关键点")
        print("• 足部运动状态精确识别算法")
        print("• 多维度特征提取和分析")
        print("• 机器学习成绩预测模型")
        print("• 个性化训练建议生成")
        
    except Exception as e:
        print(f"\n执行过程中出现错误: {e}")
        print("请检查数据文件是否完整，或联系开发者")
        sys.exit(1)
    
    print_header("系统执行完成")
    print("感谢使用立定跳远建模系统！")

def run_single_problem(problem_number):
    """运行单个问题"""
    if problem_number == 1:
        print_header("单独运行问题1")
        solver = Problem1Solver()
        return solver.solve_problem1()
    elif problem_number == 2:
        print_header("单独运行问题2")
        solver = Problem2Solver()
        return solver.solve_problem2()
    elif problem_number == 3:
        print_header("单独运行问题3")
        solver = Problem3Solver()
        return solver.solve_problem3()
    elif problem_number == 4:
        print_header("单独运行问题4")
        solver = Problem4Solver()
        return solver.solve_problem4()
    else:
        print(f"无效的问题编号: {problem_number}")
        return None

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        try:
            problem_num = int(sys.argv[1])
            if 1 <= problem_num <= 4:
                run_single_problem(problem_num)
            else:
                print("请输入1-4之间的问题编号")
        except ValueError:
            print("请输入有效的问题编号(1-4)")
    else:
        # 运行完整系统
        main()
