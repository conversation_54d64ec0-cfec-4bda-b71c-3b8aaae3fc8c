"""
立定跳远核心分析系统
基于重心轨迹、关键指标、关节角度的综合分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class JumpAnalysisCore:
    """立定跳远核心分析系统"""

    def __init__(self):
        # MediaPipe 33个关键点定义
        self.keypoint_names = {
            0: 'nose', 1: 'left_eye_inner', 2: 'left_eye', 3: 'left_eye_outer',
            4: 'right_eye_inner', 5: 'right_eye', 6: 'right_eye_outer',
            7: 'left_ear', 8: 'right_ear', 9: 'mouth_left', 10: 'mouth_right',
            11: 'left_shoulder', 12: 'right_shoulder', 13: 'left_elbow', 14: 'right_elbow',
            15: 'left_wrist', 16: 'right_wrist', 17: 'left_pinky', 18: 'right_pinky',
            19: 'left_index', 20: 'right_index', 21: 'left_thumb', 22: 'right_thumb',
            23: 'left_hip', 24: 'right_hip', 25: 'left_knee', 26: 'right_knee',
            27: 'left_ankle', 28: 'right_ankle', 29: 'left_heel', 30: 'right_heel',
            31: 'left_foot_index', 32: 'right_foot_index'
        }

        # 足部相关关键点索引
        self.foot_keypoints = {
            'left_ankle': 27, 'right_ankle': 28,
            'left_heel': 29, 'right_heel': 30,
            'left_foot_index': 31, 'right_foot_index': 32
        }

        # 身体核心关键点索引
        self.body_keypoints = {
            'left_hip': 23, 'right_hip': 24,
            'left_knee': 25, 'right_knee': 26,
            'left_shoulder': 11, 'right_shoulder': 12,
            'left_elbow': 13, 'right_elbow': 14,
            'left_wrist': 15, 'right_wrist': 16
        }

        # 兼容性关键点定义
        self.keypoints = {
            'left_hip': 23, 'right_hip': 24,
            'left_knee': 25, 'right_knee': 26,
            'left_ankle': 27, 'right_ankle': 28,
            'left_shoulder': 11, 'right_shoulder': 12
        }

        self.pipeline = None
        self.feature_names = []

        # 数据存储
        self.all_data = {}
        self.physical_data = None
    
    def load_data(self, position_file, score=None):
        """加载位置数据"""
        df = pd.read_excel(position_file)
        coordinates = []
        for frame_idx in range(len(df)):
            frame_points = []
            for point_idx in range(33):
                x_col, y_col = f"{point_idx}_X", f"{point_idx}_Y"
                if x_col in df.columns and y_col in df.columns:
                    frame_points.append([df.iloc[frame_idx][x_col], df.iloc[frame_idx][y_col]])
            coordinates.append(frame_points)
        
        return {'coordinates': np.array(coordinates), 'score': score}

    def load_all_data(self):
        """加载所有附件数据"""
        print("正在加载所有附件数据...")

        # 1. 加载附件1数据（运动者1和2）
        self._load_attachment1_data()

        # 2. 加载附件3数据（姿势调整前后对比）
        self._load_attachment3_data()

        # 3. 加载附件4数据（体质报告）
        self._load_physical_data()

        # 4. 加载附件5数据（运动者11）
        self._load_attachment5_data()

        print(f"数据加载完成，共{len(self.all_data)}个运动员数据")
        return self.all_data

    def _load_attachment1_data(self):
        """加载附件1：运动者1和2的数据"""
        # 读取成绩
        with open('附件/附件1/运动者1和运动者2的跳远成绩.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            import re
            scores = re.findall(r'(\d+\.?\d*)', content)
            athlete1_score = float(scores[0]) if len(scores) > 0 else None
            athlete2_score = float(scores[1]) if len(scores) > 1 else None

        # 加载运动者1
        data1 = self.load_data('附件/附件1/运动者1的跳远位置信息.xlsx', athlete1_score)
        self.all_data['运动者1'] = {
            'coordinates': data1['coordinates'],
            'score': athlete1_score,
            'type': '基础数据',
            'source': '附件1'
        }

        # 加载运动者2
        data2 = self.load_data('附件/附件1/运动者2的跳远位置信息.xlsx', athlete2_score)
        self.all_data['运动者2'] = {
            'coordinates': data2['coordinates'],
            'score': athlete2_score,
            'type': '基础数据',
            'source': '附件1'
        }

    def _load_attachment3_data(self):
        """加载附件3：姿势调整前后对比数据"""
        # 解析调整前成绩
        before_scores = self._parse_scores('附件/附件3/姿势调整前/运动者姿势调整前的跳远成绩.txt')

        # 解析调整后成绩
        after_scores = self._parse_scores('附件/附件3/姿势调整后/运动者姿势调整后的跳远成绩.txt')

        # 加载调整前数据
        import os
        before_dir = '附件/附件3/姿势调整前'
        for filename in os.listdir(before_dir):
            if filename.endswith('.xlsx'):
                # 解析文件名获取运动者和次数信息
                parts = filename.replace('.xlsx', '').split('第')
                if len(parts) >= 2:
                    athlete_name = parts[0]
                    attempt = parts[1].replace('次的跳远位置信息', '')

                    # 获取对应成绩
                    score = before_scores.get(athlete_name, {}).get(f'第{attempt}次', None)

                    # 加载数据
                    try:
                        data = self.load_data(os.path.join(before_dir, filename), score)
                        key = f'{athlete_name}_调整前_第{attempt}次'
                        self.all_data[key] = {
                            'coordinates': data['coordinates'],
                            'score': score,
                            'type': '调整前',
                            'athlete': athlete_name,
                            'attempt': attempt,
                            'source': '附件3'
                        }
                    except Exception as e:
                        print(f"加载{filename}失败: {e}")

        # 加载调整后数据
        after_dir = '附件/附件3/姿势调整后'
        for filename in os.listdir(after_dir):
            if filename.endswith('.xlsx'):
                # 解析文件名
                parts = filename.replace('.xlsx', '').replace('调整后', '').split('第')
                if len(parts) >= 2:
                    athlete_name = parts[0]
                    attempt = parts[1].replace('次的跳远位置信息', '')

                    # 获取对应成绩
                    score = after_scores.get(f'{athlete_name}调整后', {}).get(f'第{attempt}次', None)

                    # 加载数据
                    try:
                        data = self.load_data(os.path.join(after_dir, filename), score)
                        key = f'{athlete_name}_调整后_第{attempt}次'
                        self.all_data[key] = {
                            'coordinates': data['coordinates'],
                            'score': score,
                            'type': '调整后',
                            'athlete': athlete_name,
                            'attempt': attempt,
                            'source': '附件3'
                        }
                    except Exception as e:
                        print(f"加载{filename}失败: {e}")

    def _parse_scores(self, score_file):
        """解析成绩文件"""
        scores = {}
        with open(score_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        current_athlete = None
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if '运动者' in line and '第' not in line:
                current_athlete = line
                scores[current_athlete] = {}
            elif '第' in line and '次' in line and current_athlete:
                parts = line.split()
                if len(parts) >= 2:
                    attempt = parts[0]
                    score_str = parts[1].replace('米', '')
                    try:
                        score = float(score_str)
                        scores[current_athlete][attempt] = score
                    except:
                        pass

        return scores

    def _load_physical_data(self):
        """加载附件4：体质报告数据"""
        try:
            self.physical_data = pd.read_excel('附件/附件4.xlsx')
            print(f"体质报告加载成功，包含{len(self.physical_data)}名运动员的数据")
        except Exception as e:
            print(f"体质报告加载失败: {e}")

    def _load_attachment5_data(self):
        """加载附件5：运动者11数据"""
        try:
            data11 = self.load_data('附件/附件5/运动者11的跳远位置信息.xlsx')
            self.all_data['运动者11'] = {
                'coordinates': data11['coordinates'],
                'score': None,  # 附件5没有成绩
                'type': '预测数据',
                'source': '附件5'
            }
        except Exception as e:
            print(f"运动者11数据加载失败: {e}")

    def get_physical_info(self, athlete_name):
        """获取运动员体质信息"""
        if self.physical_data is None:
            return None

        # 匹配运动员姓名
        athlete_row = self.physical_data[self.physical_data['姓名'] == athlete_name]
        if len(athlete_row) > 0:
            return athlete_row.iloc[0].to_dict()
        return None
    
    def analyze_center_trajectory(self, coordinates):
        """分析重心轨迹"""
        # 计算髋部中心作为重心
        left_hip = coordinates[:, self.keypoints['left_hip']]
        right_hip = coordinates[:, self.keypoints['right_hip']]
        center_trajectory = (left_hip + right_hip) / 2
        
        # 轨迹特征
        trajectory_features = {
            'center_x': center_trajectory[:, 0],
            'center_y': center_trajectory[:, 1],
            'velocity_x': np.gradient(center_trajectory[:, 0]),
            'velocity_y': np.gradient(center_trajectory[:, 1]),
            'acceleration_x': np.gradient(np.gradient(center_trajectory[:, 0])),
            'acceleration_y': np.gradient(np.gradient(center_trajectory[:, 1]))
        }
        
        return trajectory_features
    
    def smooth_data(self, data, window_size=5, method='savgol'):
        """数据平滑处理"""
        if len(data) < window_size:
            return data

        if method == 'savgol':
            try:
                from scipy.signal import savgol_filter
                return savgol_filter(data, window_size, 3)
            except:
                return data
        return data

    def calculate_velocity(self, positions):
        """计算速度（帧间差分）"""
        velocity = np.diff(positions, prepend=positions[0])
        return velocity

    def get_foot_positions(self, coordinates):
        """获取足部关键点位置"""
        foot_data = {}
        for name, idx in self.foot_keypoints.items():
            foot_data[name] = {
                'x': coordinates[:, idx, 0],
                'y': coordinates[:, idx, 1]
            }
        return foot_data

    def detect_takeoff_frame(self, coordinates, smooth_window=5, min_consecutive_frames=3):
        """识别起跳帧"""
        foot_data = self.get_foot_positions(coordinates)

        # 平滑足部Y坐标数据
        smoothed_foot_y = {}
        for name, data in foot_data.items():
            smoothed_foot_y[name] = self.smooth_data(data['y'], smooth_window)

        # 计算足部垂直速度
        foot_velocities = {}
        for name, y_data in smoothed_foot_y.items():
            foot_velocities[name] = self.calculate_velocity(y_data)

        # 寻找起跳时刻：双脚同时向上运动且离开地面
        frames = len(coordinates)
        takeoff_candidates = []

        # 计算地面基准线（初始几帧的足部最低位置）
        ground_level = {}
        for name, y_data in smoothed_foot_y.items():
            ground_level[name] = np.max(y_data[:10])  # Y坐标越大越接近地面（图像坐标系）

        for frame in range(10, frames - min_consecutive_frames):
            # 检查是否所有足部关键点都开始向上运动（Y坐标减小）
            all_feet_rising = True
            all_feet_off_ground = True

            for name in foot_data.keys():
                # 检查垂直速度是否为负（向上运动）
                if foot_velocities[name][frame] >= -1:  # 阈值可调整
                    all_feet_rising = False

                # 检查是否离开地面
                if smoothed_foot_y[name][frame] >= ground_level[name] - 5:  # 容差5像素
                    all_feet_off_ground = False

            if all_feet_rising and all_feet_off_ground:
                # 验证后续几帧是否持续离地
                consecutive_airborne = True
                for check_frame in range(frame + 1, min(frame + min_consecutive_frames + 1, frames)):
                    frame_airborne = True
                    for name in foot_data.keys():
                        if smoothed_foot_y[name][check_frame] >= ground_level[name] - 5:
                            frame_airborne = False
                            break
                    if not frame_airborne:
                        consecutive_airborne = False
                        break

                if consecutive_airborne:
                    takeoff_candidates.append(frame)

        # 返回第一个满足条件的帧作为起跳帧
        return takeoff_candidates[0] if takeoff_candidates else None

    def detect_landing_frame(self, coordinates, takeoff_frame, smooth_window=5, min_consecutive_frames=3):
        """识别落地帧"""
        if takeoff_frame is None:
            return None

        foot_data = self.get_foot_positions(coordinates)

        # 平滑足部Y坐标数据
        smoothed_foot_y = {}
        for name, data in foot_data.items():
            smoothed_foot_y[name] = self.smooth_data(data['y'], smooth_window)

        # 计算足部垂直速度
        foot_velocities = {}
        for name, y_data in smoothed_foot_y.items():
            foot_velocities[name] = self.calculate_velocity(y_data)

        frames = len(coordinates)
        landing_candidates = []

        # 从起跳帧之后开始搜索落地帧
        search_start = takeoff_frame + 10  # 至少滞空10帧

        # 计算滞空期间的最低Y坐标作为落地参考
        airborne_min_y = {}
        for name, y_data in smoothed_foot_y.items():
            airborne_min_y[name] = np.min(y_data[takeoff_frame:search_start])

        for frame in range(search_start, frames - min_consecutive_frames):
            # 检查是否有足部开始接触地面
            any_foot_landing = False

            for name in foot_data.keys():
                # 检查Y坐标是否开始增大（向下接近地面）
                if (smoothed_foot_y[name][frame] > airborne_min_y[name] + 10 and  # 明显下降
                    foot_velocities[name][frame] > 1):  # 向下速度
                    any_foot_landing = True
                    break

            if any_foot_landing:
                # 验证后续几帧是否持续着地
                consecutive_grounded = True
                for check_frame in range(frame + 1, min(frame + min_consecutive_frames + 1, frames)):
                    frame_grounded = False
                    for name in foot_data.keys():
                        # 检查是否有足部保持在较低位置
                        if smoothed_foot_y[name][check_frame] > airborne_min_y[name] + 8:
                            frame_grounded = True
                            break
                    if not frame_grounded:
                        consecutive_grounded = False
                        break

                if consecutive_grounded:
                    landing_candidates.append(frame)

        # 返回第一个满足条件的帧作为落地帧
        return landing_candidates[0] if landing_candidates else None

    def identify_key_moments(self, coordinates):
        """基于足部运动状态精确判断关键时刻"""
        print("正在使用足部运动状态识别关键帧...")

        # 使用新的精确算法
        takeoff_idx = self.detect_takeoff_frame(coordinates)
        landing_idx = self.detect_landing_frame(coordinates, takeoff_idx)

        # 如果新算法失败，使用备用方法
        if takeoff_idx is None or landing_idx is None:
            print("精确算法失败，使用备用方法...")
            takeoff_idx, landing_idx = self._fallback_detection(coordinates)

        print(f"识别到关键帧 - 起跳帧: {takeoff_idx}, 落地帧: {landing_idx}")
        return takeoff_idx, landing_idx

    def _fallback_detection(self, coordinates):
        """备用关键帧检测方法"""
        trajectory = self.analyze_center_trajectory(coordinates)
        center_y = trajectory['center_y']

        # 简单的重心轨迹分析
        try:
            from scipy.signal import savgol_filter
            center_y_smooth = savgol_filter(center_y, window_length=11, polyorder=3)
        except:
            center_y_smooth = center_y

        # 寻找起跳点（重心开始上升）
        takeoff_idx = 0
        for i in range(10, len(center_y_smooth) - 10):
            if center_y_smooth[i] < center_y_smooth[i-5] - 3:
                takeoff_idx = i
                break

        # 寻找落地点（重心开始下降后上升）
        landing_idx = len(center_y_smooth) - 10
        search_start = takeoff_idx + 20
        for i in range(search_start, len(center_y_smooth) - 10):
            if center_y_smooth[i] > min(center_y_smooth[takeoff_idx:i]) + 5:
                landing_idx = i
                break

        return takeoff_idx, landing_idx
    
    def calculate_joint_angles(self, coordinates, frame_range=None):
        """计算关节角度"""
        if frame_range is None:
            frame_range = range(len(coordinates))

        angles = {
            'left_knee': [],
            'right_knee': [],
            'left_hip': [],
            'right_hip': [],
            'trunk_lean': []
        }

        for frame in frame_range:
            # 左膝关节角度（大腿-小腿夹角）
            left_hip = coordinates[frame, self.keypoints['left_hip']]
            left_knee = coordinates[frame, self.keypoints['left_knee']]
            left_ankle = coordinates[frame, self.keypoints['left_ankle']]
            angles['left_knee'].append(self._calculate_angle(left_hip, left_knee, left_ankle))

            # 右膝关节角度
            right_hip = coordinates[frame, self.keypoints['right_hip']]
            right_knee = coordinates[frame, self.keypoints['right_knee']]
            right_ankle = coordinates[frame, self.keypoints['right_ankle']]
            angles['right_knee'].append(self._calculate_angle(right_hip, right_knee, right_ankle))

            # 左髋关节角度（躯干-大腿夹角）
            left_shoulder = coordinates[frame, self.keypoints['left_shoulder']]
            angles['left_hip'].append(self._calculate_angle(left_shoulder, left_hip, left_knee))

            # 右髋关节角度
            right_shoulder = coordinates[frame, self.keypoints['right_shoulder']]
            angles['right_hip'].append(self._calculate_angle(right_shoulder, right_hip, right_knee))

            # 躯干前倾角度（相对于垂直方向）
            trunk_angle = self._calculate_trunk_angle(left_shoulder, right_shoulder, left_hip, right_hip)
            angles['trunk_lean'].append(trunk_angle)

        return angles

    def _calculate_angle(self, p1, p2, p3):
        """计算三点构成的角度"""
        v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
        v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
        angle = np.arccos(cos_angle)
        return np.degrees(angle)

    def _calculate_trunk_angle(self, left_shoulder, right_shoulder, left_hip, right_hip):
        """计算躯干相对于垂直方向的角度"""
        # 计算肩部中点和髋部中点
        shoulder_center = np.array([(left_shoulder[0] + right_shoulder[0]) / 2,
                                   (left_shoulder[1] + right_shoulder[1]) / 2])
        hip_center = np.array([(left_hip[0] + right_hip[0]) / 2,
                              (left_hip[1] + right_hip[1]) / 2])

        # 计算躯干向量
        trunk_vector = hip_center - shoulder_center

        # 垂直向量（向下为正）
        vertical_vector = np.array([0, 1])

        # 计算角度
        cos_angle = np.dot(trunk_vector, vertical_vector) / (np.linalg.norm(trunk_vector) * np.linalg.norm(vertical_vector) + 1e-8)
        cos_angle = np.clip(cos_angle, -1, 1)
        angle = np.arccos(cos_angle)
        return np.degrees(angle)

    def _calculate_knee_angles(self, coordinates):
        """计算膝关节角度（简化版，用于备用检测）"""
        angles = []
        for frame in coordinates:
            # 右腿膝关节角度
            hip = frame[self.keypoints['right_hip']]
            knee = frame[self.keypoints['right_knee']]
            ankle = frame[self.keypoints['right_ankle']]

            v1 = hip - knee
            v2 = ankle - knee
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
            angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
            angles.append(angle)

        return np.array(angles)
    
    def extract_key_indicators(self, coordinates, takeoff_idx, landing_idx):
        """提取关键指标：滞空时间、水平位移、腾空高度"""
        fps = 30  # 假设30fps
        
        # 1. 滞空时间
        airtime = (landing_idx - takeoff_idx) / fps
        
        # 2. 水平位移
        trajectory = self.analyze_center_trajectory(coordinates)
        horizontal_displacement = abs(trajectory['center_x'][landing_idx] - trajectory['center_x'][takeoff_idx])
        
        # 3. 腾空高度
        airtime_y = trajectory['center_y'][takeoff_idx:landing_idx]
        if len(airtime_y) > 0:
            max_height = trajectory['center_y'][takeoff_idx] - np.min(airtime_y)  # 图像坐标系
        else:
            max_height = 0
        
        return {
            'airtime': airtime,
            'horizontal_displacement': horizontal_displacement,
            'max_height': max_height
        }
    
    def analyze_joint_angles(self, coordinates, takeoff_idx, landing_idx):
        """关节角度对比分析"""
        # 使用完整的关节角度计算
        all_angles = self.calculate_joint_angles(coordinates)

        # 关键时刻的角度
        prep_frame = max(0, takeoff_idx - 10)  # 准备阶段

        # 获取关键时刻的角度
        prep_knee_angle = (all_angles['left_knee'][prep_frame] + all_angles['right_knee'][prep_frame]) / 2
        takeoff_knee_angle = (all_angles['left_knee'][takeoff_idx] + all_angles['right_knee'][takeoff_idx]) / 2

        if landing_idx < len(all_angles['left_knee']):
            landing_knee_angle = (all_angles['left_knee'][landing_idx] + all_angles['right_knee'][landing_idx]) / 2
        else:
            landing_knee_angle = (all_angles['left_knee'][-1] + all_angles['right_knee'][-1]) / 2

        return {
            'prep_knee_angle': prep_knee_angle,
            'takeoff_knee_angle': takeoff_knee_angle,
            'landing_knee_angle': landing_knee_angle,
            'knee_extension': takeoff_knee_angle - prep_knee_angle,
            'all_joint_angles': all_angles,
            'prep_trunk_angle': all_angles['trunk_lean'][prep_frame],
            'takeoff_trunk_angle': all_angles['trunk_lean'][takeoff_idx],
            'landing_trunk_angle': all_angles['trunk_lean'][landing_idx] if landing_idx < len(all_angles['trunk_lean']) else all_angles['trunk_lean'][-1]
        }
    
    def comprehensive_analysis(self, coordinates):
        """综合分析"""
        # 识别关键时刻
        takeoff_idx, landing_idx = self.identify_key_moments(coordinates)
        
        # 重心轨迹分析
        trajectory = self.analyze_center_trajectory(coordinates)
        
        # 关键指标提取
        key_indicators = self.extract_key_indicators(coordinates, takeoff_idx, landing_idx)
        
        # 关节角度分析
        joint_analysis = self.analyze_joint_angles(coordinates, takeoff_idx, landing_idx)
        
        # 综合特征
        features = {
            'takeoff_frame': takeoff_idx,
            'landing_frame': landing_idx,
            **key_indicators,
            **joint_analysis,
            'takeoff_velocity_x': trajectory['velocity_x'][takeoff_idx],
            'takeoff_velocity_y': trajectory['velocity_y'][takeoff_idx],
            'max_velocity_x': np.max(np.abs(trajectory['velocity_x'][takeoff_idx:landing_idx])),
            'trajectory_stability': np.std(trajectory['center_y'][takeoff_idx:landing_idx])
        }
        
        return features, trajectory

    def extract_airborne_features(self, coordinates, takeoff_frame, landing_frame):
        """提取滞空阶段运动特征"""
        if takeoff_frame is None or landing_frame is None:
            return None

        airborne_range = range(takeoff_frame, landing_frame + 1)
        features = {}

        # 1. 滞空时间（帧数）
        features['airborne_duration'] = landing_frame - takeoff_frame

        # 2. 重心轨迹
        trajectory = self.analyze_center_trajectory(coordinates)
        com_x = trajectory['center_x'][takeoff_frame:landing_frame+1]
        com_y = trajectory['center_y'][takeoff_frame:landing_frame+1]

        features['com_trajectory'] = {
            'x': com_x,
            'y': com_y,
            'horizontal_displacement': com_x[-1] - com_x[0] if len(com_x) > 0 else 0,
            'max_height': np.min(com_y) if len(com_y) > 0 else 0,  # Y坐标越小越高
            'height_range': np.max(com_y) - np.min(com_y) if len(com_y) > 0 else 0
        }

        # 3. 关节角度变化
        joint_angles = self.calculate_joint_angles(coordinates, airborne_range)
        features['joint_angles'] = {}
        for joint, angles in joint_angles.items():
            if angles:
                features['joint_angles'][joint] = {
                    'mean': np.mean(angles),
                    'max': np.max(angles),
                    'min': np.min(angles),
                    'range': np.max(angles) - np.min(angles),
                    'trajectory': angles
                }

        # 4. 四肢动作特征
        limb_features = self._extract_limb_features(coordinates, airborne_range)
        features['limb_motion'] = limb_features

        # 5. 速度特征
        velocity_features = self._extract_velocity_features(coordinates, airborne_range)
        features['velocity'] = velocity_features

        return features

    def _extract_limb_features(self, coordinates, frame_range):
        """提取四肢动作特征"""
        limb_features = {}

        # 手臂摆动幅度
        arm_keypoints = [13, 14, 15, 16]  # 肘部和手腕
        arm_positions = {}

        for kp in arm_keypoints:
            x_data = coordinates[list(frame_range), kp, 0]
            y_data = coordinates[list(frame_range), kp, 1]
            arm_positions[kp] = {
                'x_range': np.max(x_data) - np.min(x_data),
                'y_range': np.max(y_data) - np.min(y_data)
            }

        limb_features['arm_swing'] = arm_positions

        # 腿部收缩和伸展
        leg_keypoints = [25, 26, 27, 28]  # 膝部和踝部
        leg_positions = {}

        for kp in leg_keypoints:
            x_data = coordinates[list(frame_range), kp, 0]
            y_data = coordinates[list(frame_range), kp, 1]
            leg_positions[kp] = {
                'x_range': np.max(x_data) - np.min(x_data),
                'y_range': np.max(y_data) - np.min(y_data)
            }

        limb_features['leg_motion'] = leg_positions

        return limb_features

    def _extract_velocity_features(self, coordinates, frame_range):
        """提取速度特征"""
        velocity_features = {}

        # 重心速度
        trajectory = self.analyze_center_trajectory(coordinates)
        com_x = trajectory['center_x'][list(frame_range)]
        com_y = trajectory['center_y'][list(frame_range)]

        if len(com_x) > 1:
            horizontal_velocity = self.calculate_velocity(com_x)
            vertical_velocity = self.calculate_velocity(com_y)

            velocity_features['horizontal'] = {
                'mean': np.mean(horizontal_velocity),
                'max': np.max(horizontal_velocity),
                'initial': horizontal_velocity[0],
                'final': horizontal_velocity[-1]
            }

            velocity_features['vertical'] = {
                'mean': np.mean(vertical_velocity),
                'max': np.max(vertical_velocity),
                'min': np.min(vertical_velocity),
                'initial': vertical_velocity[0],
                'final': vertical_velocity[-1]
            }

        return velocity_features

    def validate_with_performance(self, features1, features2, score1, score2):
        """利用成绩信息验证识别帧合理性"""
        validation_results = {}

        # 1. 滞空时间对比
        duration1 = features1.get('airborne_duration', 0) if features1 else 0
        duration2 = features2.get('airborne_duration', 0) if features2 else 0

        duration_consistent = (duration1 > duration2) == (score1 > score2)
        validation_results['duration_consistency'] = {
            'consistent': duration_consistent,
            'athlete1_duration': duration1,
            'athlete2_duration': duration2,
            'expected': f"运动员1({score1}m)应比运动员2({score2}m)滞空时间更长"
        }

        # 2. 水平位移对比
        if features1 and features2:
            displacement1 = features1.get('horizontal_displacement', 0)
            displacement2 = features2.get('horizontal_displacement', 0)

            displacement_consistent = (displacement1 > displacement2) == (score1 > score2)
            validation_results['displacement_consistency'] = {
                'consistent': displacement_consistent,
                'athlete1_displacement': displacement1,
                'athlete2_displacement': displacement2,
                'pixel_ratio': displacement1 / displacement2 if displacement2 != 0 else float('inf'),
                'actual_ratio': score1 / score2,
                'expected': f"运动员1应比运动员2水平位移更大"
            }

            # 3. 腾空高度对比
            height1 = features1.get('max_height', 0)
            height2 = features2.get('max_height', 0)

            # 注意：Y坐标越小表示越高
            height_consistent = (height1 < height2) == (score1 > score2)
            validation_results['height_consistency'] = {
                'consistent': height_consistent,
                'athlete1_max_height': height1,
                'athlete2_max_height': height2,
                'expected': f"运动员1应比运动员2腾空高度更高（Y坐标更小）"
            }

        return validation_results
    
    def train_model(self, training_data, scores):
        """训练预测模型"""
        # 特征预处理
        df = pd.DataFrame(training_data)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df = df[numeric_cols].fillna(df[numeric_cols].mean())
        
        self.feature_names = df.columns.tolist()
        
        # 创建训练管道
        self.pipeline = Pipeline([
            ('scaler', StandardScaler()),
            ('model', LinearRegression())
        ])
        
        # 训练模型
        X = df.values
        y = np.array(scores)
        self.pipeline.fit(X, y)
        
        # 返回训练结果
        y_pred = self.pipeline.predict(X)
        from sklearn.metrics import r2_score, mean_absolute_error
        
        return {
            'r2_score': r2_score(y, y_pred),
            'mae': mean_absolute_error(y, y_pred),
            'feature_count': len(self.feature_names)
        }
    
    def predict_score(self, coordinates):
        """预测成绩"""
        if self.pipeline is None:
            raise ValueError("模型未训练")
        
        features, _ = self.comprehensive_analysis(coordinates)
        
        # 准备预测数据
        feature_df = pd.DataFrame([features])
        for col in self.feature_names:
            if col not in feature_df.columns:
                feature_df[col] = 0
        
        feature_df = feature_df[self.feature_names]
        predicted_score = self.pipeline.predict(feature_df)[0]
        
        return predicted_score, features
    
    def generate_training_advice(self, features):
        """生成训练建议"""
        advice = []
        
        # 基于关键指标的建议
        if features['airtime'] < 0.4:
            advice.append("滞空时间偏短，建议加强下肢爆发力训练")
        
        if features['max_height'] < 20:
            advice.append("腾空高度不足，建议改善起跳技术和核心力量")
        
        if features['knee_extension'] < 30:
            advice.append("膝关节伸展不充分，建议练习完全伸展的起跳动作")
        
        if features['trajectory_stability'] > 15:
            advice.append("腾空期身体稳定性不足，建议加强核心稳定性训练")
        
        return advice

    def analyze_all_athletes(self):
        """分析所有运动员数据"""
        if not self.all_data:
            self.load_all_data()

        results = {}
        print("\n开始分析所有运动员数据...")

        for athlete_key, athlete_data in self.all_data.items():
            try:
                print(f"\n分析 {athlete_key}...")

                # 综合分析
                features, trajectory = self.comprehensive_analysis(athlete_data['coordinates'])

                # 获取体质信息
                athlete_name = athlete_key.split('_')[0] if '_' in athlete_key else athlete_key
                physical_info = self.get_physical_info(athlete_name)

                # 存储结果
                results[athlete_key] = {
                    'features': features,
                    'trajectory': trajectory,
                    'score': athlete_data['score'],
                    'type': athlete_data['type'],
                    'source': athlete_data['source'],
                    'physical_info': physical_info
                }

                # 打印关键信息
                print(f"  起跳帧: {features['takeoff_frame']}, 落地帧: {features['landing_frame']}")
                print(f"  滞空时间: {features['airtime']:.3f}秒")
                print(f"  水平位移: {features['horizontal_displacement']:.2f}")
                print(f"  腾空高度: {features['max_height']:.2f}")
                if athlete_data['score']:
                    print(f"  实际成绩: {athlete_data['score']:.2f}米")

            except Exception as e:
                print(f"  分析失败: {e}")
                continue

        return results

    def compare_before_after(self, results):
        """对比调整前后的效果"""
        print("\n=== 姿势调整前后对比分析 ===")

        # 按运动员分组
        athletes = {}
        for key, data in results.items():
            if '调整前' in key or '调整后' in key:
                athlete_name = key.split('_')[0]
                if athlete_name not in athletes:
                    athletes[athlete_name] = {'调整前': [], '调整后': []}

                if '调整前' in key:
                    athletes[athlete_name]['调整前'].append((key, data))
                else:
                    athletes[athlete_name]['调整后'].append((key, data))

        # 对比分析
        comparison_results = {}
        for athlete_name, data in athletes.items():
            if data['调整前'] and data['调整后']:
                print(f"\n{athlete_name} 调整效果分析:")

                # 计算平均值
                before_scores = [d[1]['score'] for d in data['调整前'] if d[1]['score']]
                after_scores = [d[1]['score'] for d in data['调整后'] if d[1]['score']]

                before_airtimes = [d[1]['features']['airtime'] for d in data['调整前']]
                after_airtimes = [d[1]['features']['airtime'] for d in data['调整后']]

                before_heights = [d[1]['features']['max_height'] for d in data['调整前']]
                after_heights = [d[1]['features']['max_height'] for d in data['调整后']]

                if before_scores and after_scores:
                    avg_before_score = np.mean(before_scores)
                    avg_after_score = np.mean(after_scores)
                    score_improvement = avg_after_score - avg_before_score

                    print(f"  成绩改善: {avg_before_score:.2f}米 → {avg_after_score:.2f}米 ({score_improvement:+.2f}米)")

                if before_airtimes and after_airtimes:
                    avg_before_airtime = np.mean(before_airtimes)
                    avg_after_airtime = np.mean(after_airtimes)
                    airtime_change = avg_after_airtime - avg_before_airtime

                    print(f"  滞空时间: {avg_before_airtime:.3f}秒 → {avg_after_airtime:.3f}秒 ({airtime_change:+.3f}秒)")

                if before_heights and after_heights:
                    avg_before_height = np.mean(before_heights)
                    avg_after_height = np.mean(after_heights)
                    height_change = avg_after_height - avg_before_height

                    print(f"  腾空高度: {avg_before_height:.2f} → {avg_after_height:.2f} ({height_change:+.2f})")

                # 存储对比结果
                comparison_results[athlete_name] = {
                    'score_improvement': score_improvement if before_scores and after_scores else 0,
                    'airtime_change': airtime_change if before_airtimes and after_airtimes else 0,
                    'height_change': height_change if before_heights and after_heights else 0,
                    'before_data': data['调整前'],
                    'after_data': data['调整后']
                }

        return comparison_results

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("=" * 80)
        print("立定跳远综合分析报告")
        print("=" * 80)

        # 分析所有数据
        results = self.analyze_all_athletes()

        # 基础数据分析（附件1）
        print("\n【基础数据分析 - 附件1】")
        print("-" * 50)
        for key, data in results.items():
            if data['source'] == '附件1':
                print(f"{key}: 成绩{data['score']:.2f}米, 滞空时间{data['features']['airtime']:.3f}秒")

        # 姿势调整效果分析（附件3）
        comparison_results = self.compare_before_after(results)

        # 体质数据关联分析（附件4）
        print("\n【体质数据关联分析 - 附件4】")
        print("-" * 50)
        if self.physical_data is not None:
            for _, row in self.physical_data.iterrows():
                athlete_name = row['姓名']
                print(f"{athlete_name}: 年龄{row['年龄 (岁)']}岁, 身高{row['身高 (cm)']}cm, "
                      f"体重{row['体重 (kg)']}kg, 体脂率{row['体脂率 (%)']}%")

        # 预测数据分析（附件5）
        print("\n【预测数据分析 - 附件5】")
        print("-" * 50)
        for key, data in results.items():
            if data['source'] == '附件5':
                advice = self.generate_training_advice(data['features'])
                print(f"{key}: 滞空时间{data['features']['airtime']:.3f}秒, "
                      f"腾空高度{data['features']['max_height']:.2f}")
                if advice:
                    print(f"  训练建议: {'; '.join(advice)}")

        return results, comparison_results
    
    def visualize_analysis(self, coordinates, features, trajectory, save_path=None):
        """四方面对比可视化"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        takeoff_idx = features['takeoff_frame']
        landing_idx = features['landing_frame']
        
        # 1. 重心轨迹对比
        ax1 = axes[0, 0]
        ax1.plot(trajectory['center_x'], trajectory['center_y'], 'b-', alpha=0.7, label='重心轨迹')
        ax1.scatter(trajectory['center_x'][takeoff_idx], trajectory['center_y'][takeoff_idx], 
                   color='red', s=100, label='起跳点', zorder=5)
        ax1.scatter(trajectory['center_x'][landing_idx], trajectory['center_y'][landing_idx], 
                   color='green', s=100, label='落地点', zorder=5)
        ax1.set_title('重心轨迹分析')
        ax1.set_xlabel('X坐标')
        ax1.set_ylabel('Y坐标')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()
        
        # 2. 关键指标对比
        ax2 = axes[0, 1]
        indicators = ['滞空时间(s)', '水平位移', '腾空高度']
        values = [features['airtime'], features['horizontal_displacement'], features['max_height']]
        colors = ['skyblue', 'lightgreen', 'orange']
        
        bars = ax2.bar(indicators, values, color=colors)
        ax2.set_title('关键指标分析')
        ax2.set_ylabel('数值')
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.2f}', ha='center', va='bottom')
        
        # 3. 关节角度对比
        ax3 = axes[1, 0]
        angle_phases = ['准备阶段', '起跳时刻', '落地时刻']
        angle_values = [features['prep_knee_angle'], features['takeoff_knee_angle'], features['landing_knee_angle']]
        
        bars = ax3.bar(angle_phases, angle_values, color=['lightcoral', 'gold', 'lightblue'])
        ax3.set_title('关节角度对比')
        ax3.set_ylabel('膝关节角度(度)')
        for bar, value in zip(bars, angle_values):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.1f}°', ha='center', va='bottom')
        
        # 4. 关键时刻判断依据
        ax4 = axes[1, 1]
        frames = np.arange(len(trajectory['center_y']))
        ax4.plot(frames, trajectory['center_y'], 'b-', alpha=0.7, label='重心高度')
        ax4.axvline(takeoff_idx, color='red', linestyle='--', label=f'起跳帧({takeoff_idx})')
        ax4.axvline(landing_idx, color='green', linestyle='--', label=f'落地帧({landing_idx})')
        ax4.set_title('关键时刻识别')
        ax4.set_xlabel('帧数')
        ax4.set_ylabel('Y坐标')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
        
        return fig

    def visualize_comprehensive_analysis(self, results, comparison_results, save_path=None):
        """综合可视化分析"""
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))

        # 1. 基础数据对比（附件1）
        ax1 = axes[0, 0]
        basic_data = {k: v for k, v in results.items() if v['source'] == '附件1'}
        if basic_data:
            names = list(basic_data.keys())
            scores = [data['score'] for data in basic_data.values()]
            airtimes = [data['features']['airtime'] for data in basic_data.values()]

            x = np.arange(len(names))
            width = 0.35

            ax1_twin = ax1.twinx()
            bars1 = ax1.bar(x - width/2, scores, width, label='成绩(米)', color='skyblue')
            bars2 = ax1_twin.bar(x + width/2, airtimes, width, label='滞空时间(秒)', color='lightcoral')

            ax1.set_xlabel('运动员')
            ax1.set_ylabel('成绩(米)', color='blue')
            ax1_twin.set_ylabel('滞空时间(秒)', color='red')
            ax1.set_title('基础数据对比（附件1）')
            ax1.set_xticks(x)
            ax1.set_xticklabels(names)
            ax1.legend(loc='upper left')
            ax1_twin.legend(loc='upper right')

        # 2. 姿势调整效果对比（附件3）
        ax2 = axes[0, 1]
        if comparison_results:
            athletes = list(comparison_results.keys())
            improvements = [data['score_improvement'] for data in comparison_results.values()]

            colors = ['green' if imp > 0 else 'red' for imp in improvements]
            bars = ax2.bar(athletes, improvements, color=colors)
            ax2.set_title('姿势调整成绩改善效果')
            ax2.set_ylabel('成绩改善(米)')
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # 添加数值标签
            for bar, imp in zip(bars, improvements):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{imp:+.2f}', ha='center', va='bottom' if imp > 0 else 'top')

        # 3. 体质数据分布（附件4）
        ax3 = axes[1, 0]
        if self.physical_data is not None:
            # 身高体重散点图
            heights = self.physical_data['身高 (cm)']
            weights = self.physical_data['体重 (kg)']
            genders = self.physical_data['性别']

            for gender in ['男', '女']:
                mask = genders == gender
                ax3.scatter(heights[mask], weights[mask],
                           label=gender, alpha=0.7, s=100)

            ax3.set_xlabel('身高 (cm)')
            ax3.set_ylabel('体重 (kg)')
            ax3.set_title('运动员体质分布（附件4）')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 4. 关键指标统计
        ax4 = axes[1, 1]
        all_airtimes = []
        all_heights = []
        all_displacements = []

        for data in results.values():
            if data['features']:
                all_airtimes.append(data['features']['airtime'])
                all_heights.append(data['features']['max_height'])
                all_displacements.append(data['features']['horizontal_displacement'])

        if all_airtimes:
            indicators = ['滞空时间', '腾空高度', '水平位移']
            means = [np.mean(all_airtimes), np.mean(all_heights), np.mean(all_displacements)]
            stds = [np.std(all_airtimes), np.std(all_heights), np.std(all_displacements)]

            x = np.arange(len(indicators))
            bars = ax4.bar(x, means, yerr=stds, capsize=5, color=['lightblue', 'lightgreen', 'orange'])
            ax4.set_title('关键指标统计')
            ax4.set_ylabel('数值')
            ax4.set_xticks(x)
            ax4.set_xticklabels(indicators)

            # 添加数值标签
            for bar, mean, std in zip(bars, means, stds):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + std,
                        f'{mean:.2f}±{std:.2f}', ha='center', va='bottom')

        # 5. 调整前后对比雷达图
        ax5 = plt.subplot(3, 2, 5, projection='polar')
        if comparison_results:
            # 选择改善最明显的运动员
            best_athlete = max(comparison_results.items(), key=lambda x: x[1]['score_improvement'])
            athlete_name, athlete_data = best_athlete

            # 获取调整前后的平均特征
            before_data = athlete_data['before_data']
            after_data = athlete_data['after_data']

            if before_data and after_data:
                before_features = np.mean([d[1]['features']['airtime'] for d in before_data])
                after_features = np.mean([d[1]['features']['airtime'] for d in after_data])

                before_heights = np.mean([d[1]['features']['max_height'] for d in before_data])
                after_heights = np.mean([d[1]['features']['max_height'] for d in after_data])

                categories = ['滞空时间', '腾空高度', '成绩']
                before_values = [before_features, before_heights,
                               np.mean([d[1]['score'] for d in before_data if d[1]['score']])]
                after_values = [after_features, after_heights,
                              np.mean([d[1]['score'] for d in after_data if d[1]['score']])]

                # 标准化到0-1范围
                max_vals = [max(b, a) for b, a in zip(before_values, after_values)]
                before_norm = [b/m if m > 0 else 0 for b, m in zip(before_values, max_vals)]
                after_norm = [a/m if m > 0 else 0 for a, m in zip(after_values, max_vals)]

                angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
                before_norm += before_norm[:1]
                after_norm += after_norm[:1]
                angles += angles[:1]

                ax5.plot(angles, before_norm, 'o-', linewidth=2, label='调整前', color='red')
                ax5.fill(angles, before_norm, alpha=0.25, color='red')
                ax5.plot(angles, after_norm, 'o-', linewidth=2, label='调整后', color='blue')
                ax5.fill(angles, after_norm, alpha=0.25, color='blue')

                ax5.set_xticks(angles[:-1])
                ax5.set_xticklabels(categories)
                ax5.set_title(f'{athlete_name} 调整前后对比')
                ax5.legend()

        # 6. 训练建议汇总
        ax6 = axes[2, 1]
        ax6.axis('off')

        # 统计所有训练建议
        all_advice = []
        for data in results.values():
            if data['features']:
                advice = self.generate_training_advice(data['features'])
                all_advice.extend(advice)

        # 统计建议频次
        from collections import Counter
        advice_counts = Counter(all_advice)

        if advice_counts:
            advice_text = "训练建议统计:\n\n"
            for i, (advice, count) in enumerate(advice_counts.most_common(5), 1):
                advice_text += f"{i}. {advice} ({count}次)\n\n"

            ax6.text(0.05, 0.95, advice_text, transform=ax6.transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"综合分析图已保存为: {save_path}")

        plt.show()
        return fig


def main():
    """主函数：调用所有附件数据进行综合分析"""
    print("=" * 80)
    print("立定跳远综合数据分析系统")
    print("调用附件1、附件3、附件4、附件5的所有数据")
    print("=" * 80)

    # 初始化分析器
    analyzer = JumpAnalysisCore()

    # 生成综合分析报告
    results, comparison_results = analyzer.generate_comprehensive_report()

    # 创建综合可视化
    print("\n生成综合可视化分析图...")
    try:
        analyzer.visualize_comprehensive_analysis(
            results, comparison_results,
            save_path='comprehensive_analysis.png'
        )
    except Exception as e:
        print(f"可视化生成失败: {e}")

    # 训练预测模型
    print("\n训练预测模型...")
    training_features = []
    training_scores = []

    for key, data in results.items():
        if data['score'] is not None and data['features']:
            training_features.append(data['features'])
            training_scores.append(data['score'])

    if len(training_features) >= 2:
        model_results = analyzer.train_model(training_features, training_scores)
        print(f"模型训练完成: R²={model_results['r2_score']:.4f}, "
              f"MAE={model_results['mae']:.4f}")

        # 对运动者11进行预测
        if '运动者11' in results:
            try:
                predicted_score, features = analyzer.predict_score(
                    analyzer.all_data['运动者11']['coordinates']
                )
                print(f"\n运动者11预测成绩: {predicted_score:.2f}米")

                # 生成训练建议
                advice = analyzer.generate_training_advice(features)
                if advice:
                    print("训练建议:")
                    for i, suggestion in enumerate(advice, 1):
                        print(f"  {i}. {suggestion}")

            except Exception as e:
                print(f"预测失败: {e}")

    print("\n" + "=" * 80)
    print("综合数据分析完成！")
    print("生成文件:")
    print("- comprehensive_analysis.png: 综合分析图表")
    print("=" * 80)


if __name__ == "__main__":
    main()
