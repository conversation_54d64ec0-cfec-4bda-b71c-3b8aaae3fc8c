"""
问题2：分析影响运动者跳远成绩的主要因素
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler

from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
from data_preprocessing import DataPreprocessor
from problem1 import Problem1Solver

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class Problem2Solver:
    """问题2求解器：影响因素分析"""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.problem1_solver = Problem1Solver()
        self.fps = 30
    
    def extract_comprehensive_features(self, coordinates, takeoff_frame, landing_frame):
        """提取综合特征"""
        if takeoff_frame is None or landing_frame is None:
            return None

        features = {}

        # 1. 时间特征
        features['airborne_duration'] = (landing_frame - takeoff_frame) / self.fps

        # 2. 运动学特征
        motion_analysis = self.problem1_solver.analyze_airborne_motion(coordinates, takeoff_frame, landing_frame)
        features['horizontal_displacement'] = motion_analysis['horizontal_displacement']
        features['max_height'] = motion_analysis['max_height']

        # 3. 起跳技术特征
        takeoff_features = self._extract_takeoff_features(coordinates, takeoff_frame)
        features.update(takeoff_features)

        # 4. 落地技术特征
        landing_features = self._extract_landing_features(coordinates, landing_frame)
        features.update(landing_features)

        # 5. 身体姿态特征
        posture_features = self._extract_posture_features(coordinates, takeoff_frame, landing_frame)
        features.update(posture_features)

        # 6. 动力学特征
        dynamics_features = self._extract_dynamics_features(coordinates, takeoff_frame, landing_frame)
        features.update(dynamics_features)

        # 7. 起跳阶段详细特征
        detailed_takeoff_features = self._extract_detailed_takeoff_features(coordinates, takeoff_frame)
        features.update(detailed_takeoff_features)

        # 8. 落地阶段详细特征
        detailed_landing_features = self._extract_detailed_landing_features(coordinates, landing_frame)
        features.update(detailed_landing_features)

        return features
    
    def _extract_takeoff_features(self, coordinates, takeoff_frame):
        """提取起跳技术特征"""
        features = {}
        
        # 起跳角度
        left_hip = coordinates[takeoff_frame, 23]
        right_hip = coordinates[takeoff_frame, 24]
        hip_center = (left_hip + right_hip) / 2
        
        left_shoulder = coordinates[takeoff_frame, 11]
        right_shoulder = coordinates[takeoff_frame, 12]
        shoulder_center = (left_shoulder + right_shoulder) / 2
        
        body_vector = shoulder_center - hip_center
        features['takeoff_body_angle'] = np.arctan2(body_vector[1], body_vector[0]) * 180 / np.pi
        
        # 膝关节伸展程度
        prep_frame = max(0, takeoff_frame - 10)
        
        # 右膝关节角度变化
        hip_prep = coordinates[prep_frame, 24]
        knee_prep = coordinates[prep_frame, 26]
        ankle_prep = coordinates[prep_frame, 28]
        prep_knee_angle = self._calculate_angle(hip_prep, knee_prep, ankle_prep)
        
        hip_takeoff = coordinates[takeoff_frame, 24]
        knee_takeoff = coordinates[takeoff_frame, 26]
        ankle_takeoff = coordinates[takeoff_frame, 28]
        takeoff_knee_angle = self._calculate_angle(hip_takeoff, knee_takeoff, ankle_takeoff)
        
        features['knee_extension'] = takeoff_knee_angle - prep_knee_angle
        
        # 起跳速度
        if takeoff_frame >= 5:
            hip_before = (coordinates[takeoff_frame-5, 23] + coordinates[takeoff_frame-5, 24]) / 2
            hip_takeoff = (coordinates[takeoff_frame, 23] + coordinates[takeoff_frame, 24]) / 2
            velocity = np.linalg.norm(hip_takeoff - hip_before) / (5 / self.fps)
            features['takeoff_velocity'] = velocity
        else:
            features['takeoff_velocity'] = 0
        
        return features
    
    def _extract_landing_features(self, coordinates, landing_frame):
        """提取落地技术特征"""
        features = {}
        
        # 落地角度
        left_hip = coordinates[landing_frame, 23]
        right_hip = coordinates[landing_frame, 24]
        hip_center = (left_hip + right_hip) / 2
        
        left_shoulder = coordinates[landing_frame, 11]
        right_shoulder = coordinates[landing_frame, 12]
        shoulder_center = (left_shoulder + right_shoulder) / 2
        
        body_vector = shoulder_center - hip_center
        features['landing_body_angle'] = np.arctan2(body_vector[1], body_vector[0]) * 180 / np.pi
        
        # 落地膝关节角度
        hip_land = coordinates[landing_frame, 24]
        knee_land = coordinates[landing_frame, 26]
        ankle_land = coordinates[landing_frame, 28]
        features['landing_knee_angle'] = self._calculate_angle(hip_land, knee_land, ankle_land)
        
        return features
    
    def _extract_posture_features(self, coordinates, takeoff_frame, landing_frame):
        """提取身体姿态特征"""
        features = {}
        
        # 滞空期间身体稳定性
        airborne_range = range(takeoff_frame, landing_frame + 1)
        trunk_angles = []
        
        for frame in airborne_range:
            left_shoulder = coordinates[frame, 11]
            right_shoulder = coordinates[frame, 12]
            left_hip = coordinates[frame, 23]
            right_hip = coordinates[frame, 24]
            
            shoulder_center = (left_shoulder + right_shoulder) / 2
            hip_center = (left_hip + right_hip) / 2
            trunk_vector = hip_center - shoulder_center
            
            angle = np.arctan2(trunk_vector[1], trunk_vector[0]) * 180 / np.pi
            trunk_angles.append(angle)
        
        features['body_stability'] = np.std(trunk_angles) if trunk_angles else 0
        
        # 手臂摆动幅度
        left_wrist_x = coordinates[takeoff_frame:landing_frame+1, 15, 0]
        right_wrist_x = coordinates[takeoff_frame:landing_frame+1, 16, 0]
        
        features['arm_swing_amplitude'] = (np.max(left_wrist_x) - np.min(left_wrist_x) + 
                                         np.max(right_wrist_x) - np.min(right_wrist_x)) / 2
        
        return features
    
    def _extract_dynamics_features(self, coordinates, takeoff_frame, landing_frame):
        """提取动力学特征"""
        features = {}
        
        # 重心轨迹分析
        left_hip = coordinates[:, 23]
        right_hip = coordinates[:, 24]
        com = (left_hip + right_hip) / 2
        
        # 起跳前的准备动作幅度
        prep_start = max(0, takeoff_frame - 20)
        prep_com = com[prep_start:takeoff_frame]
        if len(prep_com) > 0:
            features['preparation_amplitude'] = np.max(prep_com[:, 1]) - np.min(prep_com[:, 1])
        else:
            features['preparation_amplitude'] = 0
        
        # 滞空期重心轨迹的抛物线拟合度
        airborne_com = com[takeoff_frame:landing_frame+1]
        if len(airborne_com) > 3:
            x = np.arange(len(airborne_com))
            y = airborne_com[:, 1]
            
            # 二次多项式拟合
            try:
                coeffs = np.polyfit(x, y, 2)
                y_fit = np.polyval(coeffs, x)
                r_squared = 1 - np.sum((y - y_fit) ** 2) / np.sum((y - np.mean(y)) ** 2)
                features['trajectory_parabolic_fit'] = r_squared
            except:
                features['trajectory_parabolic_fit'] = 0
        else:
            features['trajectory_parabolic_fit'] = 0
        
        return features

    def _extract_detailed_takeoff_features(self, coordinates, takeoff_frame):
        """提取详细的起跳特征"""
        features = {}

        # 起跳前准备阶段分析（前10帧）
        prep_start = max(0, takeoff_frame - 10)
        prep_frames = range(prep_start, takeoff_frame)

        if len(prep_frames) > 0:
            # 重心下降幅度（准备动作）
            com_trajectory = self._calculate_center_of_mass(coordinates)
            prep_com_y = com_trajectory[prep_frames, 1]
            features['prep_com_descent'] = np.max(prep_com_y) - np.min(prep_com_y)

            # 膝关节弯曲程度变化
            knee_angles = []
            for frame in prep_frames:
                hip = coordinates[frame, 24]  # 右髋
                knee = coordinates[frame, 26]  # 右膝
                ankle = coordinates[frame, 28]  # 右踝
                angle = self._calculate_angle(hip, knee, ankle)
                knee_angles.append(angle)

            if knee_angles:
                features['prep_knee_flexion_range'] = np.max(knee_angles) - np.min(knee_angles)
                features['prep_min_knee_angle'] = np.min(knee_angles)

            # 手臂预摆幅度
            left_wrist = coordinates[prep_frames, 15]  # 左手腕
            right_wrist = coordinates[prep_frames, 16]  # 右手腕
            features['prep_arm_swing_range'] = (
                (np.max(left_wrist[:, 1]) - np.min(left_wrist[:, 1])) +
                (np.max(right_wrist[:, 1]) - np.min(right_wrist[:, 1]))
            ) / 2

        # 起跳瞬间特征
        # 身体各部位的速度
        if takeoff_frame > 0:
            # 重心速度
            com_prev = (coordinates[takeoff_frame-1, 23] + coordinates[takeoff_frame-1, 24]) / 2
            com_curr = (coordinates[takeoff_frame, 23] + coordinates[takeoff_frame, 24]) / 2
            features['takeoff_com_velocity'] = np.linalg.norm(com_curr - com_prev) * self.fps

            # 足部离地速度
            foot_keypoints = [27, 28, 29, 30]  # 左右脚踝和脚跟
            foot_velocities = []
            for kp in foot_keypoints:
                foot_prev = coordinates[takeoff_frame-1, kp]
                foot_curr = coordinates[takeoff_frame, kp]
                velocity = np.linalg.norm(foot_curr - foot_prev) * self.fps
                foot_velocities.append(velocity)
            features['takeoff_foot_velocity'] = np.mean(foot_velocities)

        # 起跳角度分析
        left_hip = coordinates[takeoff_frame, 23]
        right_hip = coordinates[takeoff_frame, 24]
        left_shoulder = coordinates[takeoff_frame, 11]
        right_shoulder = coordinates[takeoff_frame, 12]

        hip_center = (left_hip + right_hip) / 2
        shoulder_center = (left_shoulder + right_shoulder) / 2
        body_vector = shoulder_center - hip_center

        # 身体前倾角度
        features['takeoff_body_lean'] = np.arctan2(body_vector[0], -body_vector[1]) * 180 / np.pi

        # 双腿蹬伸协调性
        left_knee_angle = self._calculate_angle(coordinates[takeoff_frame, 23],
                                               coordinates[takeoff_frame, 25],
                                               coordinates[takeoff_frame, 27])
        right_knee_angle = self._calculate_angle(coordinates[takeoff_frame, 24],
                                                coordinates[takeoff_frame, 26],
                                                coordinates[takeoff_frame, 28])
        features['takeoff_leg_symmetry'] = abs(left_knee_angle - right_knee_angle)

        return features

    def _extract_detailed_landing_features(self, coordinates, landing_frame):
        """提取详细的落地特征"""
        features = {}

        # 落地准备阶段分析（前5帧）
        prep_start = max(0, landing_frame - 5)
        prep_frames = range(prep_start, landing_frame)

        if len(prep_frames) > 0:
            # 腿部准备动作
            knee_angles = []
            for frame in prep_frames:
                hip = coordinates[frame, 24]  # 右髋
                knee = coordinates[frame, 26]  # 右膝
                ankle = coordinates[frame, 28]  # 右踝
                angle = self._calculate_angle(hip, knee, ankle)
                knee_angles.append(angle)

            if knee_angles:
                features['landing_prep_knee_extension'] = np.max(knee_angles) - np.min(knee_angles)

        # 落地瞬间特征
        # 身体姿态
        left_hip = coordinates[landing_frame, 23]
        right_hip = coordinates[landing_frame, 24]
        left_shoulder = coordinates[landing_frame, 11]
        right_shoulder = coordinates[landing_frame, 12]

        hip_center = (left_hip + right_hip) / 2
        shoulder_center = (left_shoulder + right_shoulder) / 2
        body_vector = shoulder_center - hip_center

        # 落地身体角度
        features['landing_body_lean'] = np.arctan2(body_vector[0], -body_vector[1]) * 180 / np.pi

        # 双腿落地协调性
        left_knee_angle = self._calculate_angle(coordinates[landing_frame, 23],
                                               coordinates[landing_frame, 25],
                                               coordinates[landing_frame, 27])
        right_knee_angle = self._calculate_angle(coordinates[landing_frame, 24],
                                                coordinates[landing_frame, 26],
                                                coordinates[landing_frame, 28])
        features['landing_leg_symmetry'] = abs(left_knee_angle - right_knee_angle)

        # 足部着地角度
        left_ankle = coordinates[landing_frame, 27]
        left_heel = coordinates[landing_frame, 29]
        left_toe = coordinates[landing_frame, 31]

        if np.linalg.norm(left_toe - left_heel) > 0:
            foot_vector = left_toe - left_heel
            features['landing_foot_angle'] = np.arctan2(foot_vector[1], foot_vector[0]) * 180 / np.pi
        else:
            features['landing_foot_angle'] = 0

        # 落地后缓冲阶段分析（后5帧）
        buffer_end = min(len(coordinates), landing_frame + 6)
        buffer_frames = range(landing_frame, buffer_end)

        if len(buffer_frames) > 1:
            # 重心下降幅度（缓冲效果）
            com_trajectory = self._calculate_center_of_mass(coordinates)
            buffer_com_y = com_trajectory[buffer_frames, 1]
            features['landing_buffer_descent'] = np.max(buffer_com_y) - np.min(buffer_com_y)

            # 膝关节缓冲角度变化
            knee_angles = []
            for frame in buffer_frames:
                if frame < len(coordinates):
                    hip = coordinates[frame, 24]
                    knee = coordinates[frame, 26]
                    ankle = coordinates[frame, 28]
                    angle = self._calculate_angle(hip, knee, ankle)
                    knee_angles.append(angle)

            if len(knee_angles) > 1:
                features['landing_knee_buffer_range'] = np.max(knee_angles) - np.min(knee_angles)

        return features

    def _calculate_center_of_mass(self, coordinates):
        """计算重心位置"""
        left_hip = coordinates[:, 23]
        right_hip = coordinates[:, 24]
        return (left_hip + right_hip) / 2
    
    def _calculate_angle(self, p1, p2, p3):
        """计算三点构成的角度"""
        v1 = p1 - p2
        v2 = p3 - p2
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
        return angle
    
    def analyze_before_after_improvement(self, attachment3_data):
        """分析姿势调整前后的改善效果"""
        print("分析姿势调整前后的改善效果...")
        
        improvement_analysis = {}
        
        # 按运动员分组
        athletes = {}
        for phase in ['before', 'after']:
            for key, data in attachment3_data[phase].items():
                athlete_name = data['athlete']
                if athlete_name not in athletes:
                    athletes[athlete_name] = {'before': [], 'after': []}
                athletes[athlete_name][phase].append(data)
        
        for athlete_name, data in athletes.items():
            if data['before'] and data['after']:
                print(f"\n分析{athlete_name}:")
                
                # 计算调整前后的平均成绩
                before_scores = [d['score'] for d in data['before'] if d['score']]
                after_scores = [d['score'] for d in data['after'] if d['score']]
                
                if before_scores and after_scores:
                    avg_before = np.mean(before_scores)
                    avg_after = np.mean(after_scores)
                    improvement = avg_after - avg_before
                    
                    print(f"  调整前平均成绩: {avg_before:.2f}米")
                    print(f"  调整后平均成绩: {avg_after:.2f}米")
                    print(f"  成绩改善: {improvement:+.2f}米")
                    
                    # 提取技术特征变化
                    before_features = []
                    after_features = []
                    
                    for d in data['before']:
                        if d['coordinates'] is not None:
                            takeoff = self.problem1_solver.detect_takeoff_frame(d['coordinates'])
                            landing = self.problem1_solver.detect_landing_frame(d['coordinates'], takeoff)
                            if takeoff and landing:
                                features = self.extract_comprehensive_features(d['coordinates'], takeoff, landing)
                                if features:
                                    before_features.append(features)
                    
                    for d in data['after']:
                        if d['coordinates'] is not None:
                            takeoff = self.problem1_solver.detect_takeoff_frame(d['coordinates'])
                            landing = self.problem1_solver.detect_landing_frame(d['coordinates'], takeoff)
                            if takeoff and landing:
                                features = self.extract_comprehensive_features(d['coordinates'], takeoff, landing)
                                if features:
                                    after_features.append(features)
                    
                    if before_features and after_features:
                        feature_changes = self._analyze_feature_changes(before_features, after_features)
                        
                        improvement_analysis[athlete_name] = {
                            'score_improvement': improvement,
                            'before_scores': before_scores,
                            'after_scores': after_scores,
                            'feature_changes': feature_changes
                        }
        
        return improvement_analysis
    
    def _analyze_feature_changes(self, before_features, after_features):
        """分析特征变化"""
        changes = {}
        
        # 计算所有特征的平均值
        before_avg = {}
        after_avg = {}
        
        all_keys = set()
        for f in before_features + after_features:
            all_keys.update(f.keys())
        
        for key in all_keys:
            before_values = [f[key] for f in before_features if key in f]
            after_values = [f[key] for f in after_features if key in f]
            
            if before_values and after_values:
                before_avg[key] = np.mean(before_values)
                after_avg[key] = np.mean(after_values)
                changes[key] = after_avg[key] - before_avg[key]
        
        return changes
    
    def analyze_physical_factors(self, physical_data, performance_data):
        """分析体质因素对成绩的影响"""
        print("分析体质因素对成绩的影响...")

        if physical_data is None:
            print("无体质数据")
            return None, None

        # 体质数据不需要填充，直接使用
        print(f"体质数据：{len(physical_data)}名运动员")
        valid_physical_data = physical_data.copy()

        # 合并体质数据和成绩数据
        analysis_data = []

        for idx, row in valid_physical_data.iterrows():
            athlete_name = row['姓名']

            # 获取成绩改善数据
            perf_data = performance_data.get(athlete_name, {})

            # 构建完整的分析数据
            athlete_data = {
                'athlete': athlete_name,
                # 基础信息
                'age': row['年龄 (岁)'],
                'gender': row['性别'],
                'height': row['身高 (cm)'],
                'weight': row['体重 (kg)'],
                'nutrition_status': row['营养状态'],
                'body_type': row['体型'],

                # 身体成分
                'body_fat_rate': row['体脂率 (%)'],
                'muscle_weight': row['肌肉重量 (kg)'],
                'visceral_fat_level': row['内脏脂肪 (等级)'],
                'water_rate': row['水分率 (%)'],
                'bone_weight': row['骨量 (kg)'],
                'basal_metabolism': row['基础代谢 (kcal)'],
                'fat_weight': row['脂肪重量 (kg)'],
                'muscle_rate': row['肌肉率 (%)'],
                'skeletal_muscle_weight': row['骨骼肌重量 (kg)'],
                'water_weight': row['水分重量 (kg)'],
                'protein_weight': row['蛋白质重量 (kg)'],

                # 体重控制相关
                'standard_weight': row['标准体重 (kg)'],
                'weight_control': row['体重控制量 (kg)'],
                'fat_control': row['脂肪控制量 (kg)'],
                'muscle_control': row['肌肉控制量 (kg)'],
                'protein_rate': row['蛋白质率 (%)'],
                'lean_body_weight': row['去脂体重 (kg)'],
                'body_score': row['身体得分'],

                # 成绩数据
                'has_performance_data': 'score_improvement' in perf_data,
                'score_improvement': perf_data.get('score_improvement', 0),
                'before_score': np.mean(perf_data.get('before_scores', [0])),
                'after_score': np.mean(perf_data.get('after_scores', [0]))
            }

            analysis_data.append(athlete_data)

        if analysis_data:
            df = pd.DataFrame(analysis_data)

            # 分别分析有成绩数据和无成绩数据的运动员
            with_performance = df[df['has_performance_data'] == True]
            without_performance = df[df['has_performance_data'] == False]

            print(f"有成绩改善数据的运动员：{len(with_performance)}名")
            print(f"仅有体质数据的运动员：{len(without_performance)}名")

            # 相关性分析（仅针对有成绩数据的运动员）
            correlation_matrix = None
            if len(with_performance) > 1:
                # 选择数值型列进行相关性分析
                numeric_cols = [
                    'age', 'height', 'weight', 'body_fat_rate', 'muscle_weight',
                    'water_rate', 'bone_weight', 'basal_metabolism', 'muscle_rate',
                    'skeletal_muscle_weight', 'protein_rate', 'lean_body_weight',
                    'body_score', 'score_improvement', 'before_score', 'after_score'
                ]

                # 只保留存在的列
                available_cols = [col for col in numeric_cols if col in with_performance.columns]
                correlation_matrix = with_performance[available_cols].corr()

            return df, correlation_matrix

        return None, None
    
    def train_prediction_model(self, all_features, all_scores):
        """训练多个预测模型并选择最佳模型"""
        print("训练多个预测模型并选择最佳模型...")

        if len(all_features) < 5:
            print("样本数量不足，无法进行因素分析")
            return None

        # 准备数据
        feature_df = pd.DataFrame(all_features)
        feature_df = feature_df.fillna(feature_df.mean())

        X = feature_df.values
        y = np.array(all_scores)

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 划分训练集和验证集 (80%训练，20%验证)
        X_train, X_val, y_train, y_val = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, shuffle=True
        )

        print(f"训练集样本数: {len(X_train)}, 验证集样本数: {len(X_val)}")

        # 定义多个模型
        models = {
            'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10),
            'LinearRegression': LinearRegression(),
            'Ridge': Ridge(alpha=1.0, random_state=42),
            'Lasso': Lasso(alpha=0.1, random_state=42, max_iter=2000),
            'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42, max_iter=2000),
            'SVR': SVR(kernel='rbf', C=1.0, gamma='scale'),
            'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42, max_depth=6)
        }

        # 训练和评估所有模型
        model_results = {}

        for name, model in models.items():
            try:
                # 训练模型
                model.fit(X_train, y_train)

                # 预测
                y_train_pred = model.predict(X_train)
                y_val_pred = model.predict(X_val)

                # 计算指标
                train_r2 = r2_score(y_train, y_train_pred)
                val_r2 = r2_score(y_val, y_val_pred)
                train_mae = mean_absolute_error(y_train, y_train_pred)
                val_mae = mean_absolute_error(y_val, y_val_pred)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
                val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))

                model_results[name] = {
                    'model': model,
                    'train_r2': train_r2,
                    'val_r2': val_r2,
                    'train_mae': train_mae,
                    'val_mae': val_mae,
                    'train_rmse': train_rmse,
                    'val_rmse': val_rmse,
                    'overfitting': train_r2 - val_r2
                }

                print(f"  {name}:")
                print(f"    训练集 - R²: {train_r2:.4f}, MAE: {train_mae:.4f}, RMSE: {train_rmse:.4f}")
                print(f"    验证集 - R²: {val_r2:.4f}, MAE: {val_mae:.4f}, RMSE: {val_rmse:.4f}")
                print(f"    过拟合程度: {train_r2 - val_r2:.4f}")

            except Exception as e:
                print(f"  {name}: 训练失败 - {e}")
                continue

        # 选择最佳模型 (综合考虑验证集R²和过拟合程度)
        best_model = None
        best_score = -np.inf
        best_name = None

        for name, result in model_results.items():
            # 综合评分：验证集R² - 过拟合惩罚
            composite_score = result['val_r2'] - abs(result['overfitting']) * 0.1

            if composite_score > best_score:
                best_score = composite_score
                best_model = result['model']
                best_name = name

        print(f"\n最佳模型: {best_name}")
        print(f"验证集R²: {model_results[best_name]['val_r2']:.4f}")
        print(f"验证集MAE: {model_results[best_name]['val_mae']:.4f}")

        # 特征重要性分析
        feature_importance = self._get_feature_importance(best_model, feature_df.columns)

        # 特征相关性分析
        feature_correlations = feature_df.corrwith(pd.Series(y, name='score')).abs().sort_values(ascending=False)

        return {
            'best_model': best_model,
            'best_model_name': best_name,
            'best_score': model_results[best_name]['val_r2'],  # 使用验证集R²
            'train_score': model_results[best_name]['train_r2'],
            'val_mae': model_results[best_name]['val_mae'],
            'val_rmse': model_results[best_name]['val_rmse'],
            'feature_importance': feature_importance,
            'feature_correlations': feature_correlations,
            'scaler': scaler,
            'feature_names': feature_df.columns.tolist(),
            'training_data': {
                'X_train': X_train,
                'y_train': y_train,
                'X_val': X_val,
                'y_val': y_val,
                'feature_df': feature_df
            },
            'all_model_results': model_results
        }

    def _get_feature_importance(self, model, feature_names):
        """获取特征重要性"""
        if hasattr(model, 'feature_importances_'):
            # 树模型有feature_importances_属性
            importance_values = model.feature_importances_
        elif hasattr(model, 'coef_'):
            # 线性模型使用系数的绝对值
            importance_values = np.abs(model.coef_)
        else:
            # 其他模型返回空DataFrame
            return pd.DataFrame(columns=['feature', 'importance'])

        feature_importance = pd.DataFrame({
            'feature': feature_names,
            'importance': importance_values
        }).sort_values('importance', ascending=False)

        return feature_importance
    
    def visualize_factor_analysis(self, improvement_analysis, physical_analysis, model_analysis, save_path=None):
        """可视化因素分析结果"""
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))

        # 1. 成绩改善分布
        ax1 = axes[0, 0]
        if improvement_analysis:
            improvements = [data['score_improvement'] for data in improvement_analysis.values()]
            athletes = list(improvement_analysis.keys())

            colors = ['green' if imp > 0 else 'red' for imp in improvements]
            bars = ax1.bar(athletes, improvements, color=colors)
            ax1.set_title('各运动员成绩改善情况')
            ax1.set_ylabel('成绩改善(米)')
            ax1.tick_params(axis='x', rotation=45)

            for bar, imp in zip(bars, improvements):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{imp:+.2f}', ha='center', va='bottom' if imp > 0 else 'top')
        else:
            ax1.text(0.5, 0.5, '无成绩改善数据', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('成绩改善情况')

        # 2. 体质因素相关性热力图
        if physical_analysis[1] is not None:
            ax2 = axes[0, 1]
            correlation_matrix = physical_analysis[1]
            # 选择与成绩改善相关性最高的前10个因素
            score_corr = correlation_matrix['score_improvement'].abs().sort_values(ascending=False)
            top_factors = score_corr.head(10).index.tolist()

            if len(top_factors) > 1:
                subset_corr = correlation_matrix.loc[top_factors, top_factors]
                sns.heatmap(subset_corr, annot=True, cmap='coolwarm', center=0, ax=ax2,
                           fmt='.2f', square=True)
                ax2.set_title('关键体质因素相关性')
            else:
                ax2.text(0.5, 0.5, '相关性数据不足', ha='center', va='center', transform=ax2.transAxes)
        else:
            ax2.text(0.5, 0.5, '无体质相关性数据', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('体质因素相关性')

        # 3. 身体成分分析
        if physical_analysis[0] is not None:
            ax3 = axes[0, 2]
            df = physical_analysis[0]

            # 按性别分组显示身体成分
            males = df[df['gender'] == '男']
            females = df[df['gender'] == '女']

            if len(males) > 0:
                ax3.scatter(males['body_fat_rate'], males['muscle_rate'],
                           c='blue', alpha=0.7, s=100, label='男性')
            if len(females) > 0:
                ax3.scatter(females['body_fat_rate'], females['muscle_rate'],
                           c='red', alpha=0.7, s=100, label='女性')

            ax3.set_xlabel('体脂率 (%)')
            ax3.set_ylabel('肌肉率 (%)')
            ax3.set_title('身体成分分布')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 4. 特征重要性
        if model_analysis and 'feature_importance' in model_analysis:
            ax4 = axes[1, 0]
            top_features = model_analysis['feature_importance'].head(8)
            bars = ax4.barh(top_features['feature'], top_features['importance'])
            ax4.set_title(f'关键技术因素重要性\n(模型: {model_analysis.get("best_model_name", "Unknown")})')
            ax4.set_xlabel('重要性')

            # 添加数值标签
            for bar, importance in zip(bars, top_features['importance']):
                width = bar.get_width()
                ax4.text(width, bar.get_y() + bar.get_height()/2.,
                        f'{importance:.3f}', ha='left', va='center')
        else:
            ax4.text(0.5, 0.5, '无技术因素数据', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('技术因素重要性')

        # 5. 体质与成绩改善关系
        if physical_analysis[0] is not None:
            ax5 = axes[1, 1]
            df = physical_analysis[0]
            with_performance = df[df['has_performance_data'] == True]

            if len(with_performance) > 0:
                scatter = ax5.scatter(with_performance['body_score'], with_performance['score_improvement'],
                                    c=with_performance['basal_metabolism'], s=with_performance['age']*8,
                                    alpha=0.7, cmap='viridis')
                ax5.set_xlabel('身体得分')
                ax5.set_ylabel('成绩改善 (米)')
                ax5.set_title('身体得分与成绩改善关系\n(颜色=基础代谢, 大小=年龄)')
                plt.colorbar(scatter, ax=ax5, label='基础代谢 (kcal)')
            else:
                ax5.text(0.5, 0.5, '无成绩改善数据', ha='center', va='center', transform=ax5.transAxes)
                ax5.set_title('身体得分与成绩改善关系')

        # 6. 体质数据分布
        if physical_analysis[0] is not None:
            ax6 = axes[1, 2]
            df = physical_analysis[0]

            # 显示关键体质指标的分布
            key_metrics = ['body_fat_rate', 'muscle_rate', 'basal_metabolism', 'body_score']
            metric_names = ['体脂率(%)', '肌肉率(%)', '基础代谢(kcal)', '身体得分']

            # 标准化数据用于雷达图显示
            normalized_data = []
            for metric in key_metrics:
                if metric in df.columns:
                    values = df[metric].values
                    if len(values) > 0:
                        # 标准化到0-1范围
                        min_val, max_val = values.min(), values.max()
                        if max_val > min_val:
                            norm_values = (values - min_val) / (max_val - min_val)
                        else:
                            norm_values = np.ones_like(values) * 0.5
                        normalized_data.append(norm_values.mean())
                    else:
                        normalized_data.append(0)
                else:
                    normalized_data.append(0)

            # 简单的条形图显示
            bars = ax6.bar(metric_names, normalized_data, color=['skyblue', 'lightgreen', 'orange', 'pink'])
            ax6.set_title('关键体质指标平均水平')
            ax6.set_ylabel('标准化值')
            ax6.tick_params(axis='x', rotation=45)

            for bar, value in zip(bars, normalized_data):
                height = bar.get_height()
                ax6.text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.2f}', ha='center', va='bottom')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"因素分析图已保存为: {save_path}")

        plt.show()
        return fig
    
    def solve_problem2(self):
        """求解问题2：训练模型和分析影响因素"""
        print("=" * 60)
        print("问题2：分析影响运动者跳远成绩的主要因素")
        print("=" * 60)

        # 加载数据
        attachment1_data = self.preprocessor.load_attachment1_data()
        attachment3_data = self.preprocessor.load_attachment3_data()
        physical_data = self.preprocessor.load_attachment4_data()

        # 分析姿势调整效果
        improvement_analysis = self.analyze_before_after_improvement(attachment3_data)

        # 分析体质因素
        physical_analysis = self.analyze_physical_factors(physical_data, improvement_analysis)

        # 收集所有技术特征和成绩数据用于模型训练
        print("收集训练数据...")
        all_features = []
        all_scores = []

        # 添加附件1数据
        for athlete_key, athlete_data in attachment1_data.items():
            coordinates = athlete_data['coordinates']
            score = athlete_data['score']

            if score is not None:
                takeoff = self.problem1_solver.detect_takeoff_frame(coordinates)
                landing = self.problem1_solver.detect_landing_frame(coordinates, takeoff)

                if takeoff is not None and landing is not None:
                    features = self.extract_comprehensive_features(coordinates, takeoff, landing)
                    if features:
                        all_features.append(features)
                        all_scores.append(score)
                        print(f"  添加训练样本: {athlete_data['name']} - {score:.2f}米")

        # 添加附件3数据
        for phase in ['before', 'after']:
            for _, data in attachment3_data[phase].items():
                if data['score'] and data['coordinates'] is not None:
                    takeoff = self.problem1_solver.detect_takeoff_frame(data['coordinates'])
                    landing = self.problem1_solver.detect_landing_frame(data['coordinates'], takeoff)
                    if takeoff and landing:
                        features = self.extract_comprehensive_features(data['coordinates'], takeoff, landing)
                        if features:
                            all_features.append(features)
                            all_scores.append(data['score'])
                            print(f"  添加训练样本: {data['athlete']}_{phase} - {data['score']:.2f}米")

        print(f"训练数据收集完成，共{len(all_features)}个样本")

        # 训练预测模型并分析关键因素
        model_analysis = self.train_prediction_model(all_features, all_scores)

        # 可视化分析结果
        self.visualize_factor_analysis(improvement_analysis, physical_analysis, model_analysis,
                                     'problem2_factor_analysis.png')

        # 输出分析结论
        self._print_analysis_conclusions(improvement_analysis, physical_analysis, model_analysis)

        return {
            'improvement_analysis': improvement_analysis,
            'physical_analysis': physical_analysis,
            'model_analysis': model_analysis,
            'training_data': {
                'features': all_features,
                'scores': all_scores
            }
        }
    
    def _print_analysis_conclusions(self, improvement_analysis, physical_analysis, model_analysis):
        """输出分析结论"""
        print("\n" + "=" * 80)
        print("影响因素分析结论")
        print("=" * 80)

        # 1. 姿势调整效果
        if improvement_analysis:
            print("\n1. 姿势调整效果:")
            total_improvement = 0
            improvement_count = 0
            for athlete, data in improvement_analysis.items():
                improvement = data['score_improvement']
                print(f"   {athlete}: 成绩改善{improvement:+.2f}米")
                if improvement > 0:
                    total_improvement += improvement
                    improvement_count += 1

            if improvement_count > 0:
                avg_improvement = total_improvement / improvement_count
                print(f"   平均改善幅度: {avg_improvement:.3f}米")
                print(f"   改善成功率: {improvement_count}/{len(improvement_analysis)} ({improvement_count/len(improvement_analysis)*100:.1f}%)")

        # 2. 体质因素分析
        if physical_analysis[0] is not None:
            print("\n2. 体质因素分析:")
            df = physical_analysis[0]

            print(f"   总体质数据: {len(df)}名运动员")
            print(f"   有成绩数据: {len(df[df['has_performance_data']])}名运动员")

            # 基础体质统计
            print(f"\n   基础体质统计:")
            print(f"   - 平均年龄: {df['age'].mean():.1f}岁 (范围: {df['age'].min()}-{df['age'].max()}岁)")
            print(f"   - 平均身高: {df['height'].mean():.1f}cm (范围: {df['height'].min():.1f}-{df['height'].max():.1f}cm)")
            print(f"   - 平均体重: {df['weight'].mean():.1f}kg (范围: {df['weight'].min():.1f}-{df['weight'].max():.1f}kg)")
            print(f"   - 性别分布: 男性{len(df[df['gender']=='男'])}名, 女性{len(df[df['gender']=='女'])}名")

            # 身体成分分析
            print(f"\n   身体成分分析:")
            print(f"   - 平均体脂率: {df['body_fat_rate'].mean():.1f}% (范围: {df['body_fat_rate'].min():.1f}-{df['body_fat_rate'].max():.1f}%)")
            print(f"   - 平均肌肉率: {df['muscle_rate'].mean():.1f}% (范围: {df['muscle_rate'].min():.1f}-{df['muscle_rate'].max():.1f}%)")
            print(f"   - 平均基础代谢: {df['basal_metabolism'].mean():.0f}kcal (范围: {df['basal_metabolism'].min():.0f}-{df['basal_metabolism'].max():.0f}kcal)")
            print(f"   - 平均身体得分: {df['body_score'].mean():.1f}分 (范围: {df['body_score'].min()}-{df['body_score'].max()}分)")

            # 相关性分析
            if physical_analysis[1] is not None:
                corr_matrix = physical_analysis[1]
                if 'score_improvement' in corr_matrix.columns:
                    score_corr = corr_matrix['score_improvement'].abs().sort_values(ascending=False)
                    print(f"\n   与成绩改善相关性最高的体质因素:")
                    for factor, corr in score_corr.head(5).items():
                        if factor != 'score_improvement':
                            print(f"   - {factor}: 相关系数{corr:.3f}")

        # 3. 关键技术因素和模型训练结果
        if model_analysis:
            print("\n3. 关键技术因素和模型训练结果:")
            print(f"   最佳模型: {model_analysis.get('best_model_name', 'Unknown')}")
            print(f"   验证集性能: R²={model_analysis.get('best_score', 0):.4f}")
            print(f"   训练集性能: R²={model_analysis.get('train_score', 0):.4f}")
            print(f"   验证集MAE: {model_analysis.get('val_mae', 0):.4f}")
            print(f"   训练样本数: {len(model_analysis.get('training_data', {}).get('y_train', []))}个")
            print(f"   验证样本数: {len(model_analysis.get('training_data', {}).get('y_val', []))}个")

            if 'feature_importance' in model_analysis and len(model_analysis['feature_importance']) > 0:
                print("   关键技术因素重要性排序:")
                top_factors = model_analysis['feature_importance'].head(8)
                for _, row in top_factors.iterrows():
                    print(f"   - {row['feature']}: 重要性{row['importance']:.3f}")

            if 'feature_correlations' in model_analysis:
                print("   与成绩相关性最高的技术特征:")
                top_corr = model_analysis['feature_correlations'].head(5)
                for feature, corr in top_corr.items():
                    print(f"   - {feature}: 相关系数{corr:.3f}")

        # 4. 综合结论
        print(f"\n4. 综合结论:")
        if improvement_analysis:
            print(f"   - 姿势调整训练对立定跳远成绩有显著改善效果")
        if physical_analysis[0] is not None:
            df = physical_analysis[0]
            high_score_athletes = df[df['body_score'] >= 90]
            if len(high_score_athletes) > 0:
                print(f"   - {len(high_score_athletes)}名运动员身体得分≥90分，体质状况良好")

            # 性别差异分析
            males = df[df['gender'] == '男']
            females = df[df['gender'] == '女']
            if len(males) > 0 and len(females) > 0:
                male_avg_score = males['body_score'].mean()
                female_avg_score = females['body_score'].mean()
                print(f"   - 男性平均身体得分: {male_avg_score:.1f}分")
                print(f"   - 女性平均身体得分: {female_avg_score:.1f}分")

        if model_analysis:
            print(f"   - 已成功训练预测模型，可用于成绩预测和技术指导")
            print(f"   - 识别出{len(model_analysis.get('feature_importance', []))}个关键技术特征")

        print("=" * 80)


def main():
    """主函数"""
    solver = Problem2Solver()
    results = solver.solve_problem2()


if __name__ == "__main__":
    main()
