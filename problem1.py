"""
问题1：确定运动者在跳远过程中的起跳和落地时刻，并描述滞空阶段的运动过程
"""

import numpy as np
import matplotlib.pyplot as plt
from data_preprocessing import DataPreprocessor
import os
import matplotlib.pyplot as plt
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class Problem1Solver:
    """问题1求解器：关键时刻识别和滞空阶段分析"""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.fps = 30  # 假设30fps
    
    def detect_takeoff_frame(self, coordinates, smooth_window=7, min_consecutive_frames=5):
        """增强的起跳帧识别算法 - 集成改进算法"""
        # 首先尝试改进的检测方法
        try:
            improved_takeoff = self.detect_takeoff_frame_improved(coordinates)
            if improved_takeoff is not None:
                return improved_takeoff
        except Exception as e:
            print(f"改进算法失败，使用传统方法: {e}")

        # 传统方法作为备用
        foot_data = self.preprocessor.get_foot_positions(coordinates)

        # 改进的数据平滑 - 支持高斯滤波
        smoothed_foot_y = {}
        for name, data in foot_data.items():
            smoothed_foot_y[name] = self._enhanced_smooth(data['y'], smooth_window)

        # 计算足部垂直速度和加速度
        foot_velocities = {}
        foot_accelerations = {}
        for name, y_data in smoothed_foot_y.items():
            velocities = self.preprocessor.calculate_velocity(y_data)
            foot_velocities[name] = velocities
            foot_accelerations[name] = self.preprocessor.calculate_velocity(velocities)

        frames = len(coordinates)
        takeoff_candidates = []

        # 改进的地面基准线检测 - 使用更多帧和统计方法
        ground_level = {}
        baseline_frames = min(20, frames // 4)  # 使用前1/4帧或最多20帧
        for name, y_data in smoothed_foot_y.items():
            baseline_data = y_data[:baseline_frames]
            # 使用90%分位数作为地面基准，更稳定
            ground_level[name] = np.percentile(baseline_data, 90)

        # 计算重心轨迹用于辅助判断
        com_trajectory = self._calculate_center_of_mass(coordinates)
        com_velocity = self.preprocessor.calculate_velocity(com_trajectory[:, 1])

        # 从更早的帧开始检测，移除固定偏移
        start_frame = max(5, baseline_frames)

        for frame in range(start_frame, frames - min_consecutive_frames):
            # 使用更严格的速度阈值和多条件判断
            feet_conditions = []

            for name in foot_data.keys():
                # 条件1: 垂直速度足够负（向上运动）- 适度降低阈值
                velocity_condition = foot_velocities[name][frame] <= -1.8

                # 条件2: 离开地面 - 适度降低阈值
                ground_condition = smoothed_foot_y[name][frame] <= ground_level[name] - 6

                # 条件3: 加速度为负（向上加速）- 适度降低阈值
                accel_condition = foot_accelerations[name][frame] <= -0.4

                # 至少满足2个条件
                foot_score = sum([velocity_condition, ground_condition, accel_condition])
                feet_conditions.append(foot_score >= 2)

            # 重心上升检测 - 适度降低阈值
            com_rising = com_velocity[frame] <= -0.9 if frame < len(com_velocity) else False

            # 支持部分足部满足条件（至少一半满足，让起跳更早）
            feet_satisfied = sum(feet_conditions)
            required_feet = max(1, len(feet_conditions) // 2)

            if feet_satisfied >= required_feet and com_rising:
                # 更长的连续帧验证（最多15帧）
                max_check_frames = min(15, frames - frame - 1)
                consecutive_frames = 0

                for check_offset in range(1, max_check_frames + 1):
                    check_frame = frame + check_offset
                    frame_valid = True

                    # 检查足部是否持续离地 - 放宽条件
                    for name in foot_data.keys():
                        if smoothed_foot_y[name][check_frame] >= ground_level[name] - 3:
                            frame_valid = False
                            break

                    if frame_valid:
                        consecutive_frames += 1
                    else:
                        break

                # 至少需要连续3帧满足条件（降低要求让起跳更早）
                if consecutive_frames >= max(3, min_consecutive_frames - 2):
                    takeoff_candidates.append(frame)
                    break  # 找到第一个满足条件的帧就停止

        return takeoff_candidates[0] if takeoff_candidates else None

    def detect_landing_frame(self, coordinates, takeoff_frame, smooth_window=7, min_consecutive_frames=5):
        """智能的落地帧识别算法 - 集成改进算法"""
        if takeoff_frame is None:
            return None

        # 首先尝试增强版检测方法
        try:
            enhanced_landing = self.detect_landing_moment_enhanced(coordinates, takeoff_frame)
            if enhanced_landing is not None:
                return enhanced_landing
        except Exception as e:
            print(f"增强版落地算法失败，尝试改进算法: {e}")

        # 然后尝试改进的检测方法
        try:
            improved_landing = self.detect_landing_frame_improved(coordinates, takeoff_frame)
            if improved_landing is not None:
                return improved_landing
        except Exception as e:
            print(f"改进落地算法失败，使用传统方法: {e}")

        # 传统方法作为备用
        foot_data = self.preprocessor.get_foot_positions(coordinates)

        # 改进的数据平滑
        smoothed_foot_y = {}
        for name, data in foot_data.items():
            smoothed_foot_y[name] = self._enhanced_smooth(data['y'], smooth_window)

        # 计算足部垂直速度和加速度
        foot_velocities = {}
        foot_accelerations = {}
        for name, y_data in smoothed_foot_y.items():
            velocities = self.preprocessor.calculate_velocity(y_data)
            foot_velocities[name] = velocities
            foot_accelerations[name] = self.preprocessor.calculate_velocity(velocities)

        frames = len(coordinates)
        landing_candidates = []

        # 基于滞空轨迹的统计分析
        # 估计滞空期长度（通常5-20帧）
        estimated_airborne_duration = min(20, max(8, frames - takeoff_frame - 10))
        search_start = takeoff_frame + max(5, estimated_airborne_duration // 3)
        search_end = min(frames - min_consecutive_frames, takeoff_frame + estimated_airborne_duration * 2)

        # 计算滞空期的统计特征
        airborne_stats = {}
        airborne_end = min(takeoff_frame + estimated_airborne_duration, frames)

        for name, y_data in smoothed_foot_y.items():
            airborne_segment = y_data[takeoff_frame:airborne_end]
            if len(airborne_segment) > 0:
                airborne_stats[name] = {
                    'min_y': np.min(airborne_segment),
                    'mean_y': np.mean(airborne_segment),
                    'std_y': np.std(airborne_segment),
                    'max_y': np.max(airborne_segment)
                }

        # 计算重心轨迹用于辅助判断
        com_trajectory = self._calculate_center_of_mass(coordinates)
        com_velocity = self.preprocessor.calculate_velocity(com_trajectory[:, 1])
        com_acceleration = self.preprocessor.calculate_velocity(com_velocity)

        for frame in range(search_start, search_end):
            landing_conditions = []

            for name in foot_data.keys():
                if name not in airborne_stats:
                    continue

                stats = airborne_stats[name]
                current_y = smoothed_foot_y[name][frame]
                current_vel = foot_velocities[name][frame]
                current_accel = foot_accelerations[name][frame]

                # 条件1: 相对于滞空期平均位置的位移检测 - 适度提高阈值
                displacement_condition = current_y > stats['mean_y'] + stats['std_y'] * 1.2

                # 条件2: 速度向下且足够大 - 适度提高阈值
                velocity_condition = current_vel > 1.8

                # 条件3: 加速度向下（重力影响） - 适度提高阈值
                acceleration_condition = current_accel > 0.4

                # 条件4: 接近滞空期最低点附近 - 适度提高阈值
                proximity_condition = current_y > stats['min_y'] + (stats['max_y'] - stats['min_y']) * 0.7

                # 多条件验证 - 至少满足3个条件
                foot_score = sum([displacement_condition, velocity_condition,
                                acceleration_condition, proximity_condition])
                landing_conditions.append(foot_score >= 3)

            # 重心下降检测 - 适度提高阈值
            com_falling = False
            if frame < len(com_velocity):
                com_falling = com_velocity[frame] > 1.0

            # 重心加速度检测 - 适度提高阈值
            com_accel_down = False
            if frame < len(com_acceleration):
                com_accel_down = com_acceleration[frame] > 0.3

            # 降低足部落地条件要求，让落地更晚
            feet_landing = sum(landing_conditions) >= max(1, len(landing_conditions) // 3)

            if feet_landing and (com_falling or com_accel_down):
                # 验证后续帧的连续性
                consecutive_frames = 0
                max_check = min(10, frames - frame - 1)

                for check_offset in range(1, max_check + 1):
                    check_frame = frame + check_offset
                    frame_grounded = False

                    # 检查是否持续接近地面 - 提高阈值让落地更晚
                    for name in foot_data.keys():
                        if name in airborne_stats:
                            stats = airborne_stats[name]
                            if smoothed_foot_y[name][check_frame] > stats['mean_y'] + stats['std_y'] * 0.5:
                                frame_grounded = True
                                break

                    if frame_grounded:
                        consecutive_frames += 1
                    else:
                        break

                # 提高连续帧要求，让落地更晚
                if consecutive_frames >= min_consecutive_frames + 2:
                    landing_candidates.append(frame)
                    break  # 找到第一个满足条件的帧

        return landing_candidates[0] if landing_candidates else None

    def _enhanced_smooth(self, data, window_size=7, method='savgol'):
        """增强的数据平滑方法"""
        if len(data) < window_size:
            return data

        try:
            if method == 'gaussian':
                from scipy.ndimage import gaussian_filter1d
                sigma = window_size / 6.0  # 标准差
                return gaussian_filter1d(data.astype(float), sigma=sigma)
            else:  # savgol
                from scipy.signal import savgol_filter
                # 确保窗口大小为奇数
                if window_size % 2 == 0:
                    window_size += 1
                poly_order = min(3, window_size - 1)
                return savgol_filter(data, window_size, poly_order)
        except:
            # 如果平滑失败，返回原始数据
            return data

    def _calculate_center_of_mass(self, coordinates):
        """改进的重心计算 - 使用主要躯干节点"""
        frames, num_points, _ = coordinates.shape
        com_trajectory = np.zeros((frames, 2))

        # 使用主要躯干节点计算重心：肩膀和髋部
        key_points = [11, 12, 23, 24]  # 左肩、右肩、左髋、右髋

        for frame in range(frames):
            x_coords = []
            y_coords = []

            for point_idx in key_points:
                if point_idx < num_points:
                    x_coord = coordinates[frame, point_idx, 0]
                    y_coord = coordinates[frame, point_idx, 1]

                    # 检查是否为有效坐标
                    if not (np.isnan(x_coord) or np.isnan(y_coord) or
                           (x_coord == 0 and y_coord == 0)):
                        x_coords.append(x_coord)
                        y_coords.append(y_coord)

            if x_coords and y_coords:
                com_trajectory[frame, 0] = np.mean(x_coords)
                com_trajectory[frame, 1] = np.mean(y_coords)
            else:
                # 如果主要节点缺失，使用所有可用节点
                all_x = []
                all_y = []
                for p in range(num_points):
                    x_coord = coordinates[frame, p, 0]
                    y_coord = coordinates[frame, p, 1]
                    if not (np.isnan(x_coord) or np.isnan(y_coord) or
                           (x_coord == 0 and y_coord == 0)):
                        all_x.append(x_coord)
                        all_y.append(y_coord)

                com_trajectory[frame, 0] = np.mean(all_x) if all_x else 0
                com_trajectory[frame, 1] = np.mean(all_y) if all_y else 0

        return com_trajectory

    def _calculate_foot_positions(self, coordinates):
        """计算足部位置"""
        frames, num_points, _ = coordinates.shape
        foot_data = {
            'foot_min_y': np.zeros(frames),
            'foot_center_x': np.zeros(frames)
        }

        # 足部关键点：左脚踝、左脚尖、右脚踝、右脚尖
        foot_points = [27, 29, 28, 30]  # 根据MediaPipe关键点索引

        for frame in range(frames):
            foot_y_coords = []
            foot_x_coords = []

            for point_idx in foot_points:
                if point_idx < num_points:
                    x_coord = coordinates[frame, point_idx, 0]
                    y_coord = coordinates[frame, point_idx, 1]

                    if not (np.isnan(x_coord) or np.isnan(y_coord) or
                           (x_coord == 0 and y_coord == 0)):
                        foot_x_coords.append(x_coord)
                        foot_y_coords.append(y_coord)

            if foot_y_coords:
                foot_data['foot_min_y'][frame] = max(foot_y_coords)  # Y坐标越大越接近地面
                foot_data['foot_center_x'][frame] = np.mean(foot_x_coords)
            else:
                foot_data['foot_min_y'][frame] = 0
                foot_data['foot_center_x'][frame] = 0

        return foot_data

    def _analyze_horizontal_movement(self, foot_center_x):
        """分析横向移动模式，找到第一次大幅度移动"""
        print("分析横向移动模式...")

        # 计算累积横向位移
        cumulative_displacement = []
        initial_x = foot_center_x[0]

        for i in range(len(foot_center_x)):
            displacement = foot_center_x[i] - initial_x
            cumulative_displacement.append(displacement)

        cumulative_displacement = np.array(cumulative_displacement)

        # 寻找第一次大幅度正向(左到右)移动
        movement_threshold = 30  # 像素阈值
        significant_moves = []

        for i in range(1, len(cumulative_displacement)):
            # 检查位移是否超过阈值
            if abs(cumulative_displacement[i]) > movement_threshold:
                # 检查这是否是第一次达到这个位移
                is_first_time = all(abs(cumulative_displacement[:i]) <= movement_threshold)

                if is_first_time:
                    # 验证这个移动是连续的（不是跳跃式的数据错误）
                    if i >= 5:
                        recent_trend = abs(cumulative_displacement[i] - cumulative_displacement[i-5])
                        is_continuous = recent_trend > 10  # 近5帧有连续移动
                    else:
                        is_continuous = True

                    if is_continuous:
                        significant_moves.append(i)

        horizontal_analysis = {
            'cumulative_displacement': cumulative_displacement,
            'significant_moves': significant_moves,
            'movement_threshold': movement_threshold
        }

        print(f"检测到{len(significant_moves)}个重要横向移动点")
        if significant_moves:
            print(f"第一次大幅移动: 第{significant_moves[0]}帧")

        return horizontal_analysis

    def detect_takeoff_frame_improved(self, coordinates):
        """改进的起跳检测：结合横向移动和高度变化"""
        print("执行改进的起跳检测...")

        # 计算重心轨迹和足部位置
        com_trajectory = self._calculate_center_of_mass(coordinates)
        foot_data = self._calculate_foot_positions(coordinates)

        # 计算速度
        com_velocity_x = self.preprocessor.calculate_velocity(com_trajectory[:, 0])
        com_velocity_y = self.preprocessor.calculate_velocity(com_trajectory[:, 1])

        # 分析横向移动模式
        h_analysis = self._analyze_horizontal_movement(foot_data['foot_center_x'])

        if h_analysis['significant_moves']:
            # 从第一次大幅横向移动附近开始精确定位起跳
            first_major_move = h_analysis['significant_moves'][0]

            # 在大幅移动前后10帧内寻找精确起跳时刻
            search_start = max(0, first_major_move - 10)
            search_end = min(len(com_trajectory) - 15, first_major_move + 5)

            print(f"在第{search_start}-{search_end}帧范围内精确定位起跳时刻")

            best_takeoff = None
            max_combined_score = 0

            for i in range(search_start, search_end):
                # 评分标准1: 高度增益
                if i + 15 < len(com_trajectory):
                    height_gain = com_trajectory[i, 1] - np.min(com_trajectory[i:i+15, 1])
                    height_score = min(100, height_gain * 5)  # 高度增益评分
                else:
                    height_score = 0

                # 评分标准2: 足部离地
                if i > 5:
                    ground_baseline = np.mean(foot_data['foot_min_y'][:i])
                else:
                    ground_baseline = np.mean(foot_data['foot_min_y'][:10])

                foot_lift = foot_data['foot_min_y'][i] - ground_baseline
                foot_score = min(100, abs(foot_lift) * 10) if abs(foot_lift) > 0 else 0

                # 评分标准3: 向前速度
                forward_velocity = com_velocity_x[i] if i < len(com_velocity_x) else 0
                velocity_score = min(100, abs(forward_velocity) * 2) if abs(forward_velocity) > 0 else 0

                # 评分标准4: 后续横向位移
                if i + 20 < len(foot_data['foot_center_x']):
                    future_displacement = abs(foot_data['foot_center_x'][i+20] - foot_data['foot_center_x'][i])
                    displacement_score = min(100, future_displacement)
                else:
                    displacement_score = 0

                # 综合评分
                combined_score = height_score * 0.3 + foot_score * 0.2 + velocity_score * 0.2 + displacement_score * 0.3

                if combined_score > max_combined_score and combined_score > 40:  # 最低分数要求
                    max_combined_score = combined_score
                    best_takeoff = i

                if combined_score > 60:  # 发现高分候选
                    print(f"候选帧{i}: 综合得分{combined_score:.1f} (高度{height_score:.0f}+足部{foot_score:.0f}+速度{velocity_score:.0f}+位移{displacement_score:.0f})")

            if best_takeoff:
                takeoff_frame = best_takeoff
                print(f"起跳时刻确定: 第{takeoff_frame}帧, 综合得分: {max_combined_score:.1f}")
            else:
                takeoff_frame = first_major_move
                print(f"使用横向移动点作为起跳时刻: 第{takeoff_frame}帧")
        else:
            # 如果没有明显横向移动，使用传统方法
            print("未检测到明显横向移动，使用备用检测...")
            takeoff_frame = self._traditional_takeoff_detection(com_trajectory, com_velocity_y)

        return takeoff_frame

    def _traditional_takeoff_detection(self, com_trajectory, com_velocity_y):
        """传统起跳检测作为备用"""
        # 寻找重心最低点附近的起跳
        min_height_idx = np.argmin(com_trajectory[:int(len(com_trajectory)*0.8), 1])

        # 从最低点开始向后寻找起跳
        for i in range(min_height_idx, min(len(com_trajectory)-10, min_height_idx + 20)):
            if i < len(com_velocity_y) and com_velocity_y[i] < -1.0:  # 向上运动（Y坐标减小）
                return i

        return min_height_idx

    def detect_landing_frame_improved(self, coordinates, takeoff_frame):
        """改进的落地检测：考虑横向移动停止和足部接地"""
        if takeoff_frame is None:
            return None

        print("执行改进的落地检测...")

        # 计算重心轨迹和足部位置
        com_trajectory = self._calculate_center_of_mass(coordinates)
        foot_data = self._calculate_foot_positions(coordinates)

        # 计算速度
        com_velocity_y = self.preprocessor.calculate_velocity(com_trajectory[:, 1])

        # 地面基准线
        ground_baseline = np.mean(foot_data['foot_min_y'][:takeoff_frame])
        print(f"地面基准线: {ground_baseline:.2f}像素")

        # 从起跳后开始搜索，确保最小飞行时间
        min_flight_frames = int(0.25 * self.fps)
        search_start = takeoff_frame + min_flight_frames

        # 检查横向移动是否基本停止 + 足部接地
        for i in range(search_start, len(foot_data['foot_min_y']) - 5):
            # 条件1: 足部重新接近地面
            near_ground = abs(foot_data['foot_min_y'][i] - ground_baseline) < 20

            # 条件2: 横向移动基本停止
            if i >= 10:
                recent_displacement = abs(foot_data['foot_center_x'][i] - foot_data['foot_center_x'][i-10])
                movement_stopped = recent_displacement < 15  # 近10帧内横向移动很小
            else:
                movement_stopped = True

            # 条件3: 重心不再明显上升
            if i < len(com_velocity_y):
                not_rising = com_velocity_y[i] >= -2.0  # Y坐标不再快速减小
            else:
                not_rising = True

            # 条件4: 后续保持相对稳定
            stable_afterwards = True
            if i + 10 < len(foot_data['foot_min_y']):
                future_foot_std = np.std(foot_data['foot_min_y'][i:i+10])
                stable_afterwards = future_foot_std < 8

            if near_ground and movement_stopped and not_rising and stable_afterwards:
                landing_frame = i
                print(f"落地时刻确定: 第{landing_frame}帧")
                return landing_frame

        # 备用方法：寻找横向移动停止后的第一个接地点
        print("使用备用落地检测...")

        for i in range(search_start, len(foot_data['foot_center_x']) - 10):
            if i >= 15:
                early_movement = abs(foot_data['foot_center_x'][i-5] - foot_data['foot_center_x'][i-15])
                recent_movement = abs(foot_data['foot_center_x'][i+5] - foot_data['foot_center_x'][i-5])

                if recent_movement < early_movement * 0.3:  # 移动显著减缓
                    if abs(foot_data['foot_min_y'][i] - ground_baseline) < 30:
                        landing_frame = i
                        print(f"备用方法确定落地: 第{landing_frame}帧")
                        return landing_frame

        # 最后备用：估算落地时刻
        estimated_flight_time = 0.5  # 估算0.5秒飞行
        landing_frame = takeoff_frame + int(estimated_flight_time * self.fps)
        print(f"估算落地时刻: 第{landing_frame}帧")

        return landing_frame

    def detect_landing_moment_enhanced(self, coordinates, takeoff_frame):
        """增强版落地时刻检测算法"""
        if takeoff_frame is None:
            return None

        print("执行增强版落地时刻检测...")

        # 计算必要的数据
        com_trajectory = self._calculate_center_of_mass(coordinates)
        foot_data = self._calculate_foot_positions(coordinates)

        # 计算速度和加速度
        com_velocity_x = self.preprocessor.calculate_velocity(com_trajectory[:, 0])
        com_velocity_y = self.preprocessor.calculate_velocity(com_trajectory[:, 1])
        com_acceleration_y = self.preprocessor.calculate_velocity(com_velocity_y)

        foot_velocity_y = self.preprocessor.calculate_velocity(foot_data['foot_min_y'])
        foot_velocity_x = self.preprocessor.calculate_velocity(foot_data['foot_center_x'])

        # 地面基准线 - 使用更稳定的计算方法
        pre_takeoff_window = max(10, takeoff_frame // 4)
        ground_baseline = np.median(foot_data['foot_min_y'][max(0, takeoff_frame-pre_takeoff_window):takeoff_frame])
        ground_std = np.std(foot_data['foot_min_y'][max(0, takeoff_frame-pre_takeoff_window):takeoff_frame])

        print(f"地面基准线: {ground_baseline:.2f}±{ground_std:.2f}像素")

        # 动态调整搜索范围
        estimated_flight_duration = self._estimate_flight_duration(com_trajectory, takeoff_frame)
        min_flight_frames = max(int(0.2 * self.fps), 6)  # 最少0.2秒飞行
        max_flight_frames = min(int(1.5 * self.fps), len(coordinates) - takeoff_frame - 5)  # 最多1.5秒

        search_start = takeoff_frame + min_flight_frames
        search_end = min(takeoff_frame + max_flight_frames, len(foot_data['foot_min_y']) - 10)

        print(f"搜索范围: 第{search_start}-{search_end}帧 (预估飞行时间: {estimated_flight_duration:.2f}秒)")

        # 多阶段检测策略
        landing_candidates = []

        # 阶段1: 基于物理特征的精确检测
        for i in range(search_start, search_end):
            score = self._calculate_landing_score(
                i, ground_baseline, ground_std, foot_data,
                com_velocity_y, com_acceleration_y, foot_velocity_y, foot_velocity_x
            )

            if score > 70:  # 高置信度候选
                landing_candidates.append((i, score))
                print(f"高置信度落地候选: 第{i}帧, 得分: {score:.1f}")

        # 选择最佳候选
        if landing_candidates:
            # 选择得分最高的候选
            best_candidate = max(landing_candidates, key=lambda x: x[1])
            landing_frame = best_candidate[0]
            print(f"选择最佳落地候选: 第{landing_frame}帧, 得分: {best_candidate[1]:.1f}")
        else:
            # 阶段2: 降低阈值的备用检测
            print("执行备用落地检测...")
            landing_frame = self._backup_landing_detection(
                search_start, search_end, ground_baseline, foot_data,
                com_velocity_y, foot_velocity_x
            )

        # 验证和优化结果
        if landing_frame:
            landing_frame = self._refine_landing_frame(
                landing_frame, ground_baseline, foot_data, com_velocity_y
            )

        return landing_frame

    def _estimate_flight_duration(self, com_trajectory, takeoff_frame):
        """估算飞行时间"""
        if takeoff_frame >= len(com_trajectory) - 10:
            return 0.5

        # 基于重心高度变化估算
        takeoff_height = com_trajectory[takeoff_frame, 1]

        # 寻找重心最高点
        search_end = min(takeoff_frame + int(0.8 * self.fps), len(com_trajectory))
        max_height_idx = np.argmin(com_trajectory[takeoff_frame:search_end, 1]) + takeoff_frame

        # 估算下降时间（通常与上升时间相近）
        rise_time = (max_height_idx - takeoff_frame) / self.fps
        estimated_total_time = rise_time * 2.2  # 下降时间略长于上升时间

        return max(0.3, min(1.2, estimated_total_time))

    def _calculate_landing_score(self, frame_idx, ground_baseline, ground_std, foot_data,
                               com_velocity_y, com_acceleration_y, foot_velocity_y, foot_velocity_x):
        """计算落地候选帧的综合得分"""
        score = 0

        # 特征1: 足部接近地面 (25%)
        foot_distance = abs(foot_data['foot_min_y'][frame_idx] - ground_baseline)
        if foot_distance < ground_std * 2:
            ground_score = 100 * (1 - foot_distance / (ground_std * 3))
        else:
            ground_score = max(0, 100 - foot_distance * 2)

        # 特征2: 足部垂直速度向下 (20%)
        if frame_idx < len(foot_velocity_y):
            foot_vel_y = foot_velocity_y[frame_idx]
            if foot_vel_y > 0:  # Y坐标增加表示向下
                velocity_score = min(100, foot_vel_y * 20)
            else:
                velocity_score = max(0, 50 + foot_vel_y * 10)
        else:
            velocity_score = 0

        # 特征3: 重心垂直速度向下 (20%)
        if frame_idx < len(com_velocity_y):
            com_vel_y = com_velocity_y[frame_idx]
            if com_vel_y > 0:  # Y坐标增加表示向下
                com_velocity_score = min(100, com_vel_y * 15)
            else:
                com_velocity_score = max(0, 50 + com_vel_y * 8)
        else:
            com_velocity_score = 0

        # 特征4: 水平移动减缓 (15%)
        if frame_idx >= 5 and frame_idx < len(foot_velocity_x) - 5:
            recent_h_vel = np.mean(np.abs(foot_velocity_x[frame_idx-2:frame_idx+3]))
            if recent_h_vel < 5:
                horizontal_score = 100
            elif recent_h_vel < 15:
                horizontal_score = 100 - (recent_h_vel - 5) * 8
            else:
                horizontal_score = max(0, 20 - recent_h_vel)
        else:
            horizontal_score = 50

        # 特征5: 重心加速度向下 (10%)
        if frame_idx < len(com_acceleration_y):
            com_accel_y = com_acceleration_y[frame_idx]
            if com_accel_y > 0:
                accel_score = min(100, com_accel_y * 25)
            else:
                accel_score = max(0, 50 + com_accel_y * 15)
        else:
            accel_score = 50

        # 特征6: 后续稳定性 (10%)
        stability_score = self._calculate_stability_score(frame_idx, foot_data)

        # 加权综合得分
        total_score = (ground_score * 0.25 + velocity_score * 0.20 +
                      com_velocity_score * 0.20 + horizontal_score * 0.15 +
                      accel_score * 0.10 + stability_score * 0.10)

        return total_score

    def _calculate_stability_score(self, frame_idx, foot_data):
        """计算后续稳定性得分"""
        if frame_idx + 10 >= len(foot_data['foot_min_y']):
            return 50

        future_window = foot_data['foot_min_y'][frame_idx:frame_idx+10]
        stability_std = np.std(future_window)

        if stability_std < 5:
            return 100
        elif stability_std < 15:
            return 100 - (stability_std - 5) * 8
        else:
            return max(0, 20 - stability_std)

    def _backup_landing_detection(self, search_start, search_end, ground_baseline, foot_data,
                                com_velocity_y, foot_velocity_x):
        """备用落地检测方法"""
        print("使用备用落地检测方法...")

        # 方法1: 寻找水平移动显著减缓的点
        for i in range(search_start, search_end - 10):
            if i >= 15:
                early_movement = np.mean(np.abs(foot_velocity_x[i-10:i-5]))
                recent_movement = np.mean(np.abs(foot_velocity_x[i-2:i+3]))

                # 移动显著减缓且接近地面
                if (recent_movement < early_movement * 0.4 and
                    abs(foot_data['foot_min_y'][i] - ground_baseline) < 25):
                    print(f"备用方法1找到落地: 第{i}帧")
                    return i

        # 方法2: 基于重心垂直速度的简单检测
        for i in range(search_start, search_end):
            if (i < len(com_velocity_y) and
                com_velocity_y[i] > 1.0 and  # 重心向下
                abs(foot_data['foot_min_y'][i] - ground_baseline) < 30):

                # 检查后续几帧是否稳定
                if i + 5 < len(foot_data['foot_min_y']):
                    future_std = np.std(foot_data['foot_min_y'][i:i+5])
                    if future_std < 12:
                        print(f"备用方法2找到落地: 第{i}帧")
                        return i

        # 方法3: 最后的估算方法
        estimated_landing = search_start + int(0.3 * self.fps)  # 起跳后0.3秒
        print(f"使用估算落地时刻: 第{estimated_landing}帧")
        return min(estimated_landing, search_end - 1)

    def _refine_landing_frame(self, landing_frame, ground_baseline, foot_data, com_velocity_y):
        """优化落地帧的精确位置"""
        print(f"优化落地帧位置: 初始第{landing_frame}帧")

        # 在候选帧前后3帧内寻找更精确的位置
        search_window = 3
        best_frame = landing_frame
        best_score = 0

        for offset in range(-search_window, search_window + 1):
            test_frame = landing_frame + offset

            if test_frame < 0 or test_frame >= len(foot_data['foot_min_y']):
                continue

            # 计算精细化得分
            ground_distance = abs(foot_data['foot_min_y'][test_frame] - ground_baseline)

            # 重心速度得分
            vel_score = 0
            if test_frame < len(com_velocity_y):
                vel_y = com_velocity_y[test_frame]
                if vel_y > 0:  # 向下
                    vel_score = min(50, vel_y * 10)

            # 综合得分（距离地面越近越好，速度向下越好）
            refine_score = max(0, 50 - ground_distance) + vel_score

            if refine_score > best_score:
                best_score = refine_score
                best_frame = test_frame

        if best_frame != landing_frame:
            print(f"落地帧优化: 第{landing_frame}帧 -> 第{best_frame}帧")

        return best_frame

    def analyze_airborne_motion(self, coordinates, takeoff_frame, landing_frame):
        """分析滞空阶段的运动过程"""
        if takeoff_frame is None or landing_frame is None:
            return None
        
        # 滞空时间
        airborne_duration = (landing_frame - takeoff_frame) / self.fps
        
        # 计算重心轨迹（使用髋部中心）
        left_hip = coordinates[:, 23]
        right_hip = coordinates[:, 24]
        center_of_mass = (left_hip + right_hip) / 2
        
        # 滞空阶段的重心轨迹
        airborne_com = center_of_mass[takeoff_frame:landing_frame+1]
        
        # 水平位移
        horizontal_displacement = abs(airborne_com[-1, 0] - airborne_com[0, 0])
        
        # 腾空高度（Y坐标最小值，因为图像坐标系Y向下为正）
        max_height = airborne_com[0, 1] - np.min(airborne_com[:, 1])
        
        # 速度分析
        com_velocity_x = np.gradient(airborne_com[:, 0])
        com_velocity_y = np.gradient(airborne_com[:, 1])
        
        # 关节角度分析
        joint_angles = self._calculate_joint_angles(coordinates, takeoff_frame, landing_frame)
        
        motion_analysis = {
            'airborne_duration': airborne_duration,
            'horizontal_displacement': horizontal_displacement,
            'max_height': max_height,
            'com_trajectory': airborne_com,
            'velocity_x': com_velocity_x,
            'velocity_y': com_velocity_y,
            'joint_angles': joint_angles,
            'takeoff_frame': takeoff_frame,
            'landing_frame': landing_frame
        }
        
        return motion_analysis
    
    def _calculate_joint_angles(self, coordinates, takeoff_frame, landing_frame):
        """计算关节角度变化"""
        angles = {'knee_angles': [], 'hip_angles': [], 'trunk_angles': []}
        
        for frame in range(takeoff_frame, landing_frame + 1):
            # 膝关节角度（右腿）
            hip = coordinates[frame, 24]
            knee = coordinates[frame, 26]
            ankle = coordinates[frame, 28]
            knee_angle = self._calculate_angle(hip, knee, ankle)
            angles['knee_angles'].append(knee_angle)
            
            # 髋关节角度
            shoulder = coordinates[frame, 12]
            hip_angle = self._calculate_angle(shoulder, hip, knee)
            angles['hip_angles'].append(hip_angle)
            
            # 躯干角度
            left_shoulder = coordinates[frame, 11]
            right_shoulder = coordinates[frame, 12]
            left_hip = coordinates[frame, 23]
            right_hip = coordinates[frame, 24]
            trunk_angle = self._calculate_trunk_angle(left_shoulder, right_shoulder, left_hip, right_hip)
            angles['trunk_angles'].append(trunk_angle)
        
        return angles
    
    def _calculate_angle(self, p1, p2, p3):
        """计算三点构成的角度"""
        v1 = p1 - p2
        v2 = p3 - p2
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
        return angle
    
    def _calculate_trunk_angle(self, left_shoulder, right_shoulder, left_hip, right_hip):
        """计算躯干角度"""
        shoulder_center = (left_shoulder + right_shoulder) / 2
        hip_center = (left_hip + right_hip) / 2
        trunk_vector = hip_center - shoulder_center
        vertical_vector = np.array([0, 1])
        cos_angle = np.dot(trunk_vector, vertical_vector) / (np.linalg.norm(trunk_vector) + 1e-8)
        angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
        return angle
    
    def visualize_motion_analysis(self, athlete_data, motion_analysis, save_path=None, prefix="analysis"):
        """可视化运动分析结果，并将每个子图各自导出为单独图片"""


        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        coordinates = athlete_data['coordinates']
        takeoff_frame = motion_analysis['takeoff_frame']
        landing_frame = motion_analysis['landing_frame']

        # 1. 足部高度变化和关键帧识别
        ax1 = axes[0, 0]
        foot_data = self.preprocessor.get_foot_positions(coordinates)

        for name, data in foot_data.items():
            smoothed_y = self.preprocessor.smooth_data(data['y'])
            ax1.plot(smoothed_y, label=name, alpha=0.7)

        ax1.axvline(takeoff_frame, color='red', linestyle='--', label=f'起跳帧({takeoff_frame})')
        ax1.axvline(landing_frame, color='green', linestyle='--', label=f'落地帧({landing_frame})')
        ax1.set_title('足部高度变化与关键帧识别')
        ax1.set_xlabel('帧数')
        ax1.set_ylabel('Y坐标')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 重心运动轨迹（仅滞空阶段）
        ax2 = axes[0, 1]
        com_trajectory = motion_analysis['com_trajectory']
        ax2.plot(com_trajectory[:, 0], com_trajectory[:, 1], 'b-', linewidth=2, label='滞空阶段重心轨迹')
        ax2.scatter(com_trajectory[0, 0], com_trajectory[0, 1], color='red', s=100, label='起跳点', zorder=5)
        ax2.scatter(com_trajectory[-1, 0], com_trajectory[-1, 1], color='green', s=100, label='落地点', zorder=5)
        ax2.set_title(f'滞空阶段重心运动轨迹\n滞空时间: {motion_analysis["airborne_duration"]:.3f}秒')
        ax2.set_xlabel('X坐标')
        ax2.set_ylabel('Y坐标')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.invert_yaxis()

        # 3. 速度变化
        ax3 = axes[1, 0]
        t_vel = np.linspace(0, motion_analysis['airborne_duration'], len(motion_analysis['velocity_x']))
        ax3.plot(t_vel, motion_analysis['velocity_x'], 'r-', label='水平速度')
        ax3.plot(t_vel, -motion_analysis['velocity_y'], 'b-', label='垂直速度')  # 负号因为Y轴向下
        ax3.set_title('滞空阶段速度变化')
        ax3.set_xlabel('时间(秒)')
        ax3.set_ylabel('速度(像素/帧)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 关节角度变化
        ax4 = axes[1, 1]
        joint_angles = motion_analysis['joint_angles']
        t_ang = np.linspace(0, motion_analysis['airborne_duration'], len(joint_angles['knee_angles']))
        ax4.plot(t_ang, joint_angles['knee_angles'], 'g-', label='膝关节角度')
        ax4.plot(t_ang, joint_angles['trunk_angles'], 'orange', label='躯干角度')
        ax4.set_title('滞空阶段关节角度变化')
        ax4.set_xlabel('时间(秒)')
        ax4.set_ylabel('角度(度)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # === 保存：整张 + 各子图 ===
        if save_path is None:
            save_dir = r"C:\Users\<USER>\Desktop\outputs\problem1"
        else:
            save_dir = save_path  # 作为目录使用

        os.makedirs(save_dir, exist_ok=True)

        # 1) 保存整张汇总图
        whole_path = os.path.join(save_dir, f"{prefix}_summary.png")
        fig.savefig(whole_path, dpi=300, bbox_inches='tight')
        print(f"已保存整张图: {whole_path}")

        # 2) 逐个保存子图（准确裁剪到各自 Axes）
        fig.canvas.draw()  # 确保渲染器可用
        renderer = fig.canvas.get_renderer()

        parts = [
            (ax1, "foot_height"),
            (ax2, "com_trajectory"),
            (ax3, "velocity"),
            (ax4, "joint_angles"),
        ]

        for ax, name in parts:
            bbox = ax.get_tightbbox(renderer).transformed(fig.dpi_scale_trans.inverted())
            # 稍微扩一圈，避免标题/标签被切掉
            bbox = bbox.expanded(1.02, 1.08)

            out_path = os.path.join(save_dir, f"{prefix}_{name}.png")
            fig.savefig(out_path, dpi=300, bbox_inches=bbox)
            print(f"已保存子图: {out_path}")

        plt.show()
        return fig
    
    def solve_problem1(self):
        """求解问题1"""
        print("=" * 60)
        print("问题1：确定起跳和落地时刻，描述滞空阶段运动过程")
        print("=" * 60)
        
        # 加载附件1数据
        attachment1_data = self.preprocessor.load_attachment1_data()
        
        results = {}
        
        for athlete_key, athlete_data in attachment1_data.items():
            print(f"\n分析{athlete_data['name']}:")
            print("-" * 30)
            
            coordinates = athlete_data['coordinates']
            
            # 识别关键时刻
            takeoff_frame = self.detect_takeoff_frame(coordinates)
            landing_frame = self.detect_landing_frame(coordinates, takeoff_frame)
            
            if takeoff_frame is not None and landing_frame is not None:
                print(f"起跳时刻: 第{takeoff_frame}帧 ({takeoff_frame/self.fps:.2f}秒)")
                print(f"落地时刻: 第{landing_frame}帧 ({landing_frame/self.fps:.2f}秒)")
                
                # 分析滞空阶段运动
                motion_analysis = self.analyze_airborne_motion(coordinates, takeoff_frame, landing_frame)
                
                print(f"滞空时间: {motion_analysis['airborne_duration']:.3f}秒")
                print(f"水平位移: {motion_analysis['horizontal_displacement']:.2f}像素")
                print(f"腾空高度: {motion_analysis['max_height']:.2f}像素")
                print(f"实际成绩: {athlete_data['score']:.2f}米")
                
                # 可视化分析
                save_dir = "problem1_outputs"
                self.visualize_motion_analysis(athlete_data, motion_analysis, save_dir, f"{athlete_data['name']}_analysis")
                
                results[athlete_key] = {
                    'athlete_data': athlete_data,
                    'motion_analysis': motion_analysis
                }
            else:
                print("关键帧识别失败")
        
        print("\n" + "=" * 60)
        print("问题1分析完成")
        print("=" * 60)
        
        return results


def main():
    """主函数"""
    solver = Problem1Solver()
    results = solver.solve_problem1()
    
    # 输出详细的运动过程描述
    print("\n滞空阶段运动过程描述:")
    print("=" * 60)
    
    for athlete_key, result in results.items():
        athlete_name = result['athlete_data']['name']
        motion = result['motion_analysis']
        
        print(f"\n{athlete_name}滞空阶段运动过程:")
        print(f"1. 起跳阶段: 第{motion['takeoff_frame']}帧，双脚离地，身体开始腾空")
        print(f"2. 上升阶段: 重心持续上升，腾空高度达到{motion['max_height']:.1f}像素")
        print(f"3. 滞空阶段: 持续{motion['airborne_duration']:.3f}秒，水平位移{motion['horizontal_displacement']:.1f}像素")
        print(f"4. 下降阶段: 重心开始下降，准备落地")
        print(f"5. 落地阶段: 第{motion['landing_frame']}帧，双脚接触地面")


if __name__ == "__main__":
    main()
